// Mock environment variables for testing
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
process.env.WHATSAPP_API_KEY = 'test-whatsapp-api-key'
process.env.WHATSAPP_API_URL = 'https://test-whatsapp-api.com'
process.env.METAL_RATES_API_URL = 'https://test-metal-rates-api.com'
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters-long'
process.env.JWT_SECRET = 'test-jwt-secret-key-32-characters-long'
process.env.CSRF_SECRET = 'test-csrf-secret-key-32-characters-long'
process.env.ENABLE_STRICT_SECURITY = 'false'
process.env.ALLOWED_ORIGINS = 'http://localhost:3000,http://127.0.0.1:3000'
process.env.MAX_FILE_SIZE = '5242880'
process.env.ALLOWED_FILE_TYPES = 'image/jpeg,image/png,image/gif,image/webp,application/pdf,text/csv'
process.env.ENABLE_IP_BLOCKING = 'false'
process.env.MAX_TRANSACTION_AMOUNT = '1000000'
process.env.DAILY_TRANSACTION_LIMIT = '5000000'
process.env.MONTHLY_TRANSACTION_LIMIT = '50000000'
process.env.LARGE_TRANSACTION_THRESHOLD = '100000'
process.env.ENABLE_SECURITY_TESTING = 'true'
process.env.ENABLE_DEBUG_LOGGING = 'false'
