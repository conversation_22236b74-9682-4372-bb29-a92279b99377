import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { z } from 'zod'

const verifyOTPSchema = z.object({
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  otp: z.string().length(6, 'OTP must be 6 digits'),
  type: z.enum(['registration', 'login', 'password_reset', 'phone_verification']),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { phone, otp, type } = verifyOTPSchema.parse(body)

    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )

    // Get current user if authenticated
    const { data: { user } } = await supabase.auth.getUser()

    // Find the OTP record
    const { data: otpRecord, error: fetchError } = await supabase
      .from('otp_verifications')
      .select('*')
      .eq('phone', phone)
      .eq('otp_type', type)
      .eq('is_verified', false)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (fetchError || !otpRecord) {
      return NextResponse.json(
        { error: 'Invalid or expired OTP' },
        { status: 400 }
      )
    }

    // Check if OTP has expired
    if (new Date() > new Date(otpRecord.expires_at)) {
      return NextResponse.json(
        { error: 'OTP has expired' },
        { status: 400 }
      )
    }

    // Check if too many attempts
    if (otpRecord.attempts >= 3) {
      return NextResponse.json(
        { error: 'Too many failed attempts. Please request a new OTP.' },
        { status: 400 }
      )
    }

    // Verify OTP
    if (otpRecord.otp_code !== otp) {
      // Increment attempts
      await supabase
        .from('otp_verifications')
        .update({ attempts: otpRecord.attempts + 1 })
        .eq('id', otpRecord.id)

      return NextResponse.json(
        { error: 'Invalid OTP' },
        { status: 400 }
      )
    }

    // Mark OTP as verified
    const { error: updateError } = await supabase
      .from('otp_verifications')
      .update({ 
        is_verified: true,
        attempts: otpRecord.attempts + 1 
      })
      .eq('id', otpRecord.id)

    if (updateError) {
      console.error('Error updating OTP record:', updateError)
      return NextResponse.json(
        { error: 'Failed to verify OTP' },
        { status: 500 }
      )
    }

    // Handle different OTP types
    switch (type) {
      case 'registration':
      case 'phone_verification':
        // Update user's phone verification status
        if (user) {
          const { error: userUpdateError } = await supabase
            .from('users')
            .update({ 
              is_phone_verified: true,
              phone: phone 
            })
            .eq('id', user.id)

          if (userUpdateError) {
            console.error('Error updating user phone verification:', userUpdateError)
          }
        }
        break

      case 'login':
        // For phone-based login, you might want to create a session here
        // This depends on your specific authentication flow
        break

      case 'password_reset':
        // Generate a password reset token or handle the reset flow
        // This would typically redirect to a password reset form
        break
    }

    return NextResponse.json({
      success: true,
      message: 'OTP verified successfully',
      type,
    })

  } catch (error) {
    console.error('Verify OTP error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
