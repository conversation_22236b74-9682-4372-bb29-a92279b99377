import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { WhatsAppService } from '@/lib/api/whatsapp'
import { generateOTP, formatPhoneForWhatsApp } from '@/lib/utils'
import { z } from 'zod'

const sendOTPSchema = z.object({
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  type: z.enum(['registration', 'login', 'password_reset', 'phone_verification']),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { phone, type } = sendOTPSchema.parse(body)

    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.set({ name, value: '', ...options })
          },
        },
      }
    )
    const whatsapp = new WhatsAppService()

    // Format phone number for WhatsApp
    const formattedPhone = formatPhoneForWhatsApp(phone)

    // Generate OTP
    const otpCode = generateOTP()
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Get current user if authenticated
    const { data: { user } } = await supabase.auth.getUser()

    // Check if phone number is already registered (for registration type)
    if (type === 'registration') {
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('phone', phone)
        .single()

      if (existingUser) {
        return NextResponse.json(
          { error: 'Phone number already registered' },
          { status: 400 }
        )
      }
    }

    // Check rate limiting - max 3 OTPs per phone per hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    const { data: recentOTPs } = await supabase
      .from('otp_verifications')
      .select('id')
      .eq('phone', phone)
      .gte('created_at', oneHourAgo.toISOString())

    if (recentOTPs && recentOTPs.length >= 3) {
      return NextResponse.json(
        { error: 'Too many OTP requests. Please try again later.' },
        { status: 429 }
      )
    }

    // Store OTP in database
    const { error: dbError } = await supabase
      .from('otp_verifications')
      .insert({
        user_id: user?.id || null,
        phone,
        otp_code: otpCode,
        otp_type: type,
        expires_at: expiresAt.toISOString(),
      })

    if (dbError) {
      console.error('Database error:', dbError)
      return NextResponse.json(
        { error: 'Failed to store OTP' },
        { status: 500 }
      )
    }

    // Send OTP via WhatsApp
    try {
      await whatsapp.sendOTP(formattedPhone, otpCode, type)
    } catch (whatsappError) {
      console.error('WhatsApp error:', whatsappError)
      // Don't fail the request if WhatsApp fails, but log it
      // In production, you might want to have a fallback SMS service
    }

    return NextResponse.json({
      success: true,
      message: 'OTP sent successfully',
      expiresAt: expiresAt.toISOString(),
    })

  } catch (error) {
    console.error('Send OTP error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
