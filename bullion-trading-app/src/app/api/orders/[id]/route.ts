import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: order, error } = await supabase
      .from('trades')
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit,
          current_price,
          image_url,
          description
        )
      `)
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (error || !order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    return NextResponse.json({ order })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, ...updateData } = body

    // Get current order
    const { data: currentOrder, error: fetchError } = await supabase
      .from('trades')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !currentOrder) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    // Handle different actions
    if (action === 'cancel') {
      // Only allow cancellation of pending orders
      if (currentOrder.status !== 'pending') {
        return NextResponse.json(
          { error: 'Only pending orders can be cancelled' },
          { status: 400 }
        )
      }

      const { data: order, error: cancelError } = await supabase
        .from('trades')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', params.id)
        .eq('user_id', user.id)
        .select(`
          *,
          bullion_products (
            id,
            name,
            symbol,
            type,
            purity,
            weight,
            weight_unit,
            current_price,
            image_url
          )
        `)
        .single()

      if (cancelError) {
        console.error('Error cancelling order:', cancelError)
        return NextResponse.json({ error: 'Failed to cancel order' }, { status: 500 })
      }

      return NextResponse.json({ order, message: 'Order cancelled successfully' })
    }

    if (action === 'execute') {
      // Only allow execution of pending orders
      if (currentOrder.status !== 'pending') {
        return NextResponse.json(
          { error: 'Only pending orders can be executed' },
          { status: 400 }
        )
      }

      try {
        // Execute the order
        await executeOrder(supabase, params.id, user.id)

        // Fetch updated order
        const { data: order, error: fetchUpdatedError } = await supabase
          .from('trades')
          .select(`
            *,
            bullion_products (
              id,
              name,
              symbol,
              type,
              purity,
              weight,
              weight_unit,
              current_price,
              image_url
            )
          `)
          .eq('id', params.id)
          .single()

        if (fetchUpdatedError) {
          throw new Error('Failed to fetch updated order')
        }

        return NextResponse.json({ order, message: 'Order executed successfully' })
      } catch (executeError) {
        console.error('Error executing order:', executeError)
        return NextResponse.json({ error: 'Failed to execute order' }, { status: 500 })
      }
    }

    // Handle general updates (for admin or system use)
    const allowedUpdates = ['notes', 'status']
    const filteredUpdates = Object.keys(updateData)
      .filter(key => allowedUpdates.includes(key))
      .reduce((obj, key) => {
        obj[key] = updateData[key]
        return obj
      }, {} as any)

    if (Object.keys(filteredUpdates).length === 0) {
      return NextResponse.json({ error: 'No valid updates provided' }, { status: 400 })
    }

    filteredUpdates.updated_at = new Date().toISOString()

    const { data: order, error: updateError } = await supabase
      .from('trades')
      .update(filteredUpdates)
      .eq('id', params.id)
      .eq('user_id', user.id)
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit,
          current_price,
          image_url
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating order:', updateError)
      return NextResponse.json({ error: 'Failed to update order' }, { status: 500 })
    }

    return NextResponse.json({ order })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if order exists and belongs to user
    const { data: order, error: fetchError } = await supabase
      .from('trades')
      .select('status')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    // Only allow deletion of cancelled or failed orders
    if (!['cancelled', 'failed'].includes(order.status)) {
      return NextResponse.json(
        { error: 'Only cancelled or failed orders can be deleted' },
        { status: 400 }
      )
    }

    const { error: deleteError } = await supabase
      .from('trades')
      .delete()
      .eq('id', params.id)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting order:', deleteError)
      return NextResponse.json({ error: 'Failed to delete order' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Order deleted successfully' })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper function to execute an order
async function executeOrder(supabase: any, orderId: string, userId: string) {
  // Get the order details
  const { data: order, error: orderError } = await supabase
    .from('trades')
    .select('*, bullion_products(*)')
    .eq('id', orderId)
    .eq('user_id', userId)
    .single()

  if (orderError || !order) {
    throw new Error('Order not found')
  }

  if (order.status !== 'pending') {
    throw new Error('Order is not in pending status')
  }

  // For sell orders, verify holdings again
  if (order.type === 'sell') {
    const { data: portfolio, error: portfolioError } = await supabase
      .from('portfolios')
      .select('quantity')
      .eq('user_id', userId)
      .eq('product_id', order.product_id)
      .single()

    if (portfolioError || !portfolio || portfolio.quantity < order.quantity) {
      throw new Error('Insufficient holdings for sell order')
    }
  }

  // Use current market price for execution
  const executionPrice = order.bullion_products.current_price
  const executionAmount = order.quantity * executionPrice
  const fees = executionAmount * 0.01 // 1% fee
  const finalAmount = executionAmount + (order.type === 'buy' ? fees : -fees)

  // Update order status to completed
  const { error: updateError } = await supabase
    .from('trades')
    .update({
      status: 'completed',
      price_per_unit: executionPrice,
      total_amount: finalAmount,
      fees,
      completed_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('id', orderId)

  if (updateError) {
    throw new Error('Failed to update order status')
  }

  // Update user portfolio
  await updatePortfolio(supabase, userId, order.product_id, order.type, order.quantity, executionPrice)
}

// Helper function to update portfolio (same as in main orders route)
async function updatePortfolio(
  supabase: any,
  userId: string,
  productId: string,
  orderType: 'buy' | 'sell',
  quantity: number,
  price: number
) {
  const { data: currentPosition, error: portfolioError } = await supabase
    .from('portfolios')
    .select('*')
    .eq('user_id', userId)
    .eq('product_id', productId)
    .single()

  if (orderType === 'buy') {
    if (currentPosition) {
      const newQuantity = currentPosition.quantity + quantity
      const newTotalInvested = currentPosition.total_invested + (quantity * price)
      const newAverageBuyPrice = newTotalInvested / newQuantity

      const { error: updateError } = await supabase
        .from('portfolios')
        .update({
          quantity: newQuantity,
          total_invested: newTotalInvested,
          average_buy_price: newAverageBuyPrice,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('product_id', productId)

      if (updateError) {
        throw new Error('Failed to update portfolio position')
      }
    } else {
      const { error: insertError } = await supabase
        .from('portfolios')
        .insert({
          user_id: userId,
          product_id: productId,
          quantity,
          average_buy_price: price,
          total_invested: quantity * price,
          current_value: 0,
          profit_loss: 0,
          profit_loss_percentage: 0
        })

      if (insertError) {
        throw new Error('Failed to create portfolio position')
      }
    }
  } else if (orderType === 'sell') {
    if (currentPosition && currentPosition.quantity >= quantity) {
      const newQuantity = currentPosition.quantity - quantity
      const soldValue = quantity * currentPosition.average_buy_price
      const newTotalInvested = currentPosition.total_invested - soldValue

      if (newQuantity > 0) {
        const { error: updateError } = await supabase
          .from('portfolios')
          .update({
            quantity: newQuantity,
            total_invested: newTotalInvested,
            last_updated: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('product_id', productId)

        if (updateError) {
          throw new Error('Failed to update portfolio position')
        }
      } else {
        const { error: deleteError } = await supabase
          .from('portfolios')
          .delete()
          .eq('user_id', userId)
          .eq('product_id', productId)

        if (deleteError) {
          throw new Error('Failed to remove portfolio position')
        }
      }
    } else {
      throw new Error('Insufficient holdings for sell order')
    }
  }
}
