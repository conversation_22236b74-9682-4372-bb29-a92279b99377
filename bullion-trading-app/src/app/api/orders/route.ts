import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query
    let query = supabase
      .from('trades')
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit,
          current_price,
          image_url
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Filter by status if provided
    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    const { data: orders, error } = await query

    if (error) {
      console.error('Error fetching orders:', error)
      return NextResponse.json({ error: 'Failed to fetch orders' }, { status: 500 })
    }

    return NextResponse.json({ orders })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      product_id,
      type,
      quantity,
      price_per_unit,
      order_method = 'market',
      limit_price,
      notes
    } = body

    // Validate required fields
    if (!product_id || !type || !quantity || !price_per_unit) {
      return NextResponse.json(
        { error: 'Missing required fields: product_id, type, quantity, price_per_unit' },
        { status: 400 }
      )
    }

    // Validate order type
    if (!['buy', 'sell'].includes(type)) {
      return NextResponse.json({ error: 'Invalid order type' }, { status: 400 })
    }

    // Validate quantity
    if (quantity <= 0) {
      return NextResponse.json({ error: 'Quantity must be greater than 0' }, { status: 400 })
    }

    // Get product details
    const { data: product, error: productError } = await supabase
      .from('bullion_products')
      .select('*')
      .eq('id', product_id)
      .eq('is_active', true)
      .single()

    if (productError || !product) {
      return NextResponse.json({ error: 'Product not found or inactive' }, { status: 404 })
    }

    // For sell orders, check if user has sufficient holdings
    if (type === 'sell') {
      const { data: portfolio, error: portfolioError } = await supabase
        .from('portfolios')
        .select('quantity')
        .eq('user_id', user.id)
        .eq('product_id', product_id)
        .single()

      if (portfolioError || !portfolio || portfolio.quantity < quantity) {
        return NextResponse.json(
          { error: 'Insufficient holdings for sell order' },
          { status: 400 }
        )
      }
    }

    // Calculate total amount and fees
    const effectivePrice = order_method === 'limit' && limit_price ? limit_price : price_per_unit
    const subtotal = quantity * effectivePrice
    const feeRate = 0.01 // 1% fee
    const fees = subtotal * feeRate
    const total_amount = subtotal + (type === 'buy' ? fees : -fees)

    // Create the order
    const orderData = {
      user_id: user.id,
      product_id,
      type: type as 'buy' | 'sell',
      quantity,
      price_per_unit: effectivePrice,
      total_amount,
      fees,
      status: 'pending' as const,
      notes: notes || null
    }

    const { data: order, error: orderError } = await supabase
      .from('trades')
      .insert(orderData)
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit,
          current_price,
          image_url
        )
      `)
      .single()

    if (orderError) {
      console.error('Error creating order:', orderError)
      return NextResponse.json({ error: 'Failed to create order' }, { status: 500 })
    }

    // For market orders, process immediately
    if (order_method === 'market') {
      try {
        await processMarketOrder(supabase, order.id, user.id)
      } catch (processError) {
        console.error('Error processing market order:', processError)
        // Order is created but processing failed - will be handled by background job
      }
    }

    return NextResponse.json({ order }, { status: 201 })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper function to process market orders
async function processMarketOrder(supabase: any, orderId: string, userId: string) {
  // Get the order details
  const { data: order, error: orderError } = await supabase
    .from('trades')
    .select('*, bullion_products(*)')
    .eq('id', orderId)
    .eq('user_id', userId)
    .single()

  if (orderError || !order) {
    throw new Error('Order not found')
  }

  // Use current market price for execution
  const executionPrice = order.bullion_products.current_price
  const executionAmount = order.quantity * executionPrice
  const fees = executionAmount * 0.01 // 1% fee
  const finalAmount = executionAmount + (order.type === 'buy' ? fees : -fees)

  // Update order status to completed
  const { error: updateError } = await supabase
    .from('trades')
    .update({
      status: 'completed',
      price_per_unit: executionPrice,
      total_amount: finalAmount,
      fees,
      completed_at: new Date().toISOString()
    })
    .eq('id', orderId)

  if (updateError) {
    throw new Error('Failed to update order status')
  }

  // Update user portfolio
  await updatePortfolio(supabase, userId, order.product_id, order.type, order.quantity, executionPrice)
}

// Helper function to update portfolio
async function updatePortfolio(
  supabase: any,
  userId: string,
  productId: string,
  orderType: 'buy' | 'sell',
  quantity: number,
  price: number
) {
  // Get current portfolio position
  const { data: currentPosition, error: portfolioError } = await supabase
    .from('portfolios')
    .select('*')
    .eq('user_id', userId)
    .eq('product_id', productId)
    .single()

  if (orderType === 'buy') {
    if (currentPosition) {
      // Update existing position
      const newQuantity = currentPosition.quantity + quantity
      const newTotalInvested = currentPosition.total_invested + (quantity * price)
      const newAverageBuyPrice = newTotalInvested / newQuantity

      const { error: updateError } = await supabase
        .from('portfolios')
        .update({
          quantity: newQuantity,
          total_invested: newTotalInvested,
          average_buy_price: newAverageBuyPrice,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('product_id', productId)

      if (updateError) {
        throw new Error('Failed to update portfolio position')
      }
    } else {
      // Create new position
      const { error: insertError } = await supabase
        .from('portfolios')
        .insert({
          user_id: userId,
          product_id: productId,
          quantity,
          average_buy_price: price,
          total_invested: quantity * price,
          current_value: 0, // Will be updated by price update job
          profit_loss: 0,
          profit_loss_percentage: 0
        })

      if (insertError) {
        throw new Error('Failed to create portfolio position')
      }
    }
  } else if (orderType === 'sell') {
    if (currentPosition && currentPosition.quantity >= quantity) {
      const newQuantity = currentPosition.quantity - quantity
      const soldValue = quantity * currentPosition.average_buy_price
      const newTotalInvested = currentPosition.total_invested - soldValue

      if (newQuantity > 0) {
        // Update remaining position
        const { error: updateError } = await supabase
          .from('portfolios')
          .update({
            quantity: newQuantity,
            total_invested: newTotalInvested,
            last_updated: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('product_id', productId)

        if (updateError) {
          throw new Error('Failed to update portfolio position')
        }
      } else {
        // Remove position entirely
        const { error: deleteError } = await supabase
          .from('portfolios')
          .delete()
          .eq('user_id', userId)
          .eq('product_id', productId)

        if (deleteError) {
          throw new Error('Failed to remove portfolio position')
        }
      }
    } else {
      throw new Error('Insufficient holdings for sell order')
    }
  }
}
