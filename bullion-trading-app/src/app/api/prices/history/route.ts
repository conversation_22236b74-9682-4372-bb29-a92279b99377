import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.set({ name, value: '', ...options })
          },
        },
      }
    )

    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('product_id')
    const symbol = searchParams.get('symbol')
    const period = searchParams.get('period') || '24h'
    const limit = parseInt(searchParams.get('limit') || '100')

    if (!productId && !symbol) {
      return NextResponse.json({ 
        error: 'Either product_id or symbol is required' 
      }, { status: 400 })
    }

    // Calculate time range based on period
    const now = new Date()
    let startTime: Date

    switch (period) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startTime = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }

    let query = supabase
      .from('price_history')
      .select(`
        *,
        bullion_products (
          name,
          symbol,
          type
        )
      `)
      .gte('timestamp', startTime.toISOString())
      .order('timestamp', { ascending: true })
      .limit(limit)

    if (productId) {
      query = query.eq('product_id', productId)
    } else if (symbol) {
      // First get the product ID by symbol
      const { data: product, error: productError } = await supabase
        .from('bullion_products')
        .select('id')
        .eq('symbol', symbol.toUpperCase())
        .single()

      if (productError || !product) {
        return NextResponse.json({ error: 'Product not found' }, { status: 404 })
      }

      query = query.eq('product_id', product.id)
    }

    const { data: history, error } = await query

    if (error) {
      console.error('Error fetching price history:', error)
      return NextResponse.json({ error: 'Failed to fetch price history' }, { status: 500 })
    }

    // Format data for charts
    const chartData = history?.map(item => ({
      timestamp: item.timestamp,
      price: parseFloat(item.price),
      currency: item.currency,
      source: item.source
    })) || []

    return NextResponse.json({ 
      success: true, 
      data: {
        period,
        symbol: symbol?.toUpperCase(),
        product_id: productId,
        history: chartData,
        count: chartData.length
      }
    })
  } catch (error) {
    console.error('Price history GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST endpoint to manually add price history (for testing/admin use)
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.set({ name, value: '', ...options })
          },
        },
      }
    )

    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { product_id, price, currency = 'INR', source = 'manual' } = body

    if (!product_id || !price) {
      return NextResponse.json({ 
        error: 'product_id and price are required' 
      }, { status: 400 })
    }

    // Insert price history record
    const { data: historyRecord, error: insertError } = await supabase
      .from('price_history')
      .insert({
        product_id,
        price: parseFloat(price),
        currency,
        source,
        timestamp: new Date().toISOString()
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error inserting price history:', insertError)
      return NextResponse.json({ error: 'Failed to insert price history' }, { status: 500 })
    }

    // Also update the current price in bullion_products
    const { error: updateError } = await supabase
      .from('bullion_products')
      .update({
        current_price: parseFloat(price),
        updated_at: new Date().toISOString()
      })
      .eq('id', product_id)

    if (updateError) {
      console.error('Error updating current price:', updateError)
      // Don't fail the request, just log the error
    }

    return NextResponse.json({ 
      success: true, 
      data: historyRecord,
      message: 'Price history added successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Price history POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
