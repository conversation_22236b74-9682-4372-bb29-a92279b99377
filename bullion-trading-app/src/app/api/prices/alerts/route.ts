import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { z } from 'zod'

const createAlertSchema = z.object({
  product_id: z.string().uuid(),
  target_price: z.number().positive(),
  condition: z.enum(['above', 'below'])
})

const updateAlertSchema = z.object({
  target_price: z.number().positive().optional(),
  condition: z.enum(['above', 'below']).optional(),
  is_active: z.boolean().optional()
})

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )

    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('product_id')
    const isActive = searchParams.get('is_active')

    let query = supabase
      .from('price_alerts')
      .select(`
        *,
        bullion_products (
          name,
          symbol,
          current_price
        )
      `)
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })

    if (productId) {
      query = query.eq('product_id', productId)
    }

    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true')
    }

    const { data: alerts, error } = await query

    if (error) {
      console.error('Error fetching price alerts:', error)
      return NextResponse.json({ error: 'Failed to fetch alerts' }, { status: 500 })
    }

    return NextResponse.json({ success: true, data: alerts })
  } catch (error) {
    console.error('Price alerts GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )

    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createAlertSchema.parse(body)

    // Check if product exists
    const { data: product, error: productError } = await supabase
      .from('bullion_products')
      .select('id, name, current_price')
      .eq('id', validatedData.product_id)
      .single()

    if (productError || !product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    // Check for existing active alert with same conditions
    const { data: existingAlert } = await supabase
      .from('price_alerts')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('product_id', validatedData.product_id)
      .eq('condition', validatedData.condition)
      .eq('is_active', true)
      .single()

    if (existingAlert) {
      return NextResponse.json({ 
        error: 'You already have an active alert for this product with the same condition' 
      }, { status: 400 })
    }

    // Create the alert
    const { data: alert, error: insertError } = await supabase
      .from('price_alerts')
      .insert({
        user_id: session.user.id,
        product_id: validatedData.product_id,
        target_price: validatedData.target_price,
        condition: validatedData.condition,
        is_active: true
      })
      .select(`
        *,
        bullion_products (
          name,
          symbol,
          current_price
        )
      `)
      .single()

    if (insertError) {
      console.error('Error creating price alert:', insertError)
      return NextResponse.json({ error: 'Failed to create alert' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      data: alert,
      message: 'Price alert created successfully'
    }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Price alerts POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )

    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const alertId = searchParams.get('id')

    if (!alertId) {
      return NextResponse.json({ error: 'Alert ID is required' }, { status: 400 })
    }

    const body = await request.json()
    const validatedData = updateAlertSchema.parse(body)

    // Update the alert
    const { data: alert, error: updateError } = await supabase
      .from('price_alerts')
      .update(validatedData)
      .eq('id', alertId)
      .eq('user_id', session.user.id)
      .select(`
        *,
        bullion_products (
          name,
          symbol,
          current_price
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating price alert:', updateError)
      return NextResponse.json({ error: 'Failed to update alert' }, { status: 500 })
    }

    if (!alert) {
      return NextResponse.json({ error: 'Alert not found' }, { status: 404 })
    }

    return NextResponse.json({ 
      success: true, 
      data: alert,
      message: 'Price alert updated successfully'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    console.error('Price alerts PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          },
        },
      }
    )

    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const alertId = searchParams.get('id')

    if (!alertId) {
      return NextResponse.json({ error: 'Alert ID is required' }, { status: 400 })
    }

    // Delete the alert
    const { error: deleteError } = await supabase
      .from('price_alerts')
      .delete()
      .eq('id', alertId)
      .eq('user_id', session.user.id)

    if (deleteError) {
      console.error('Error deleting price alert:', deleteError)
      return NextResponse.json({ error: 'Failed to delete alert' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'Price alert deleted successfully'
    })
  } catch (error) {
    console.error('Price alerts DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
