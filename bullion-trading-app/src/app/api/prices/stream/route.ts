import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { MetalRatesService, MetalRatesSSEClient } from '@/lib/api/metal-rates'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  // Verify authentication
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: any) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )

  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return new Response('Unauthorized', { status: 401 })
  }

  // Set up SSE headers
  const headers = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache, no-transform',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  })

  const encoder = new TextEncoder()
  let isConnected = true

  const stream = new ReadableStream({
    start(controller) {
      // Send initial connection message
      const initialMessage = `data: ${JSON.stringify({
        type: 'connected',
        timestamp: new Date().toISOString()
      })}\n\n`
      controller.enqueue(encoder.encode(initialMessage))

      // Set up metal rates SSE client
      const metalRatesClient = new MetalRatesSSEClient()

      // Handle rate updates from external API
      metalRatesClient.onRatesUpdate = async (rates, type) => {
        if (!isConnected) return

        try {
          // Update database with new prices
          await updatePricesInDatabase(rates, supabase)

          // Send update to client
          const message = `data: ${JSON.stringify({
            type: 'price_update',
            data: rates,
            timestamp: new Date().toISOString()
          })}\n\n`
          
          controller.enqueue(encoder.encode(message))
        } catch (error) {
          console.error('Error processing price update:', error)
        }
      }

      // Handle connection status changes
      metalRatesClient.onConnectionChange = (connected) => {
        if (!isConnected) return

        const message = `data: ${JSON.stringify({
          type: 'connection_status',
          connected,
          timestamp: new Date().toISOString()
        })}\n\n`
        
        controller.enqueue(encoder.encode(message))
      }

      // Send heartbeat every 30 seconds
      const heartbeatInterval = setInterval(() => {
        if (!isConnected) {
          clearInterval(heartbeatInterval)
          return
        }

        const heartbeat = `data: ${JSON.stringify({
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        })}\n\n`
        
        controller.enqueue(encoder.encode(heartbeat))
      }, 30000)

      // Send initial prices
      MetalRatesService.getCurrentRates()
        .then(rates => {
          if (!isConnected) return

          const message = `data: ${JSON.stringify({
            type: 'initial_prices',
            data: rates,
            timestamp: new Date().toISOString()
          })}\n\n`
          
          controller.enqueue(encoder.encode(message))
        })
        .catch(error => {
          console.error('Error fetching initial prices:', error)
        })

      // Cleanup on close
      request.signal.addEventListener('abort', () => {
        isConnected = false
        metalRatesClient.disconnect()
        clearInterval(heartbeatInterval)
        controller.close()
      })
    },

    cancel() {
      isConnected = false
    }
  })

  return new Response(stream, { headers })
}

async function updatePricesInDatabase(rates: any[], supabase: any) {
  try {
    for (const rate of rates) {
      // Update current price in bullion_products
      const { error: updateError } = await supabase
        .from('bullion_products')
        .update({
          current_price: rate.price,
          updated_at: new Date().toISOString()
        })
        .eq('symbol', rate.symbol.toUpperCase())

      if (updateError) {
        console.error('Error updating product price:', updateError)
        continue
      }

      // Insert price history record
      const { error: historyError } = await supabase
        .from('price_history')
        .insert({
          product_id: await getProductIdBySymbol(rate.symbol, supabase),
          price: rate.price,
          currency: rate.currency || 'INR',
          source: 'metal_api',
          timestamp: new Date().toISOString()
        })

      if (historyError) {
        console.error('Error inserting price history:', historyError)
      }
    }
  } catch (error) {
    console.error('Error updating prices in database:', error)
    throw error
  }
}

async function getProductIdBySymbol(symbol: string, supabase: any): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .from('bullion_products')
      .select('id')
      .eq('symbol', symbol.toUpperCase())
      .single()

    if (error || !data) {
      console.error('Error getting product ID:', error)
      return null
    }

    return data.id
  } catch (error) {
    console.error('Error in getProductIdBySymbol:', error)
    return null
  }
}
