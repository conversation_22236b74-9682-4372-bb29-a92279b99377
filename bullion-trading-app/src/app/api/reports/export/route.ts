import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'
import { ReportingService, ReportType, ReportFormat, ReportFilters } from '@/lib/services/reportingService'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const reportType = searchParams.get('type') as ReportType
    const format = searchParams.get('format') as ReportFormat || 'csv'
    
    const filters: ReportFilters = {
      date_from: searchParams.get('date_from') || undefined,
      date_to: searchParams.get('date_to') || undefined,
      product_id: searchParams.get('product_id') || undefined,
      trade_type: searchParams.get('trade_type') as any || undefined,
      status: searchParams.get('status') as any || undefined,
      financial_year: searchParams.get('financial_year') || undefined
    }

    if (!reportType) {
      return NextResponse.json({ error: 'Report type is required' }, { status: 400 })
    }

    let reportData: any
    let filename: string
    let headers: string[]
    let rows: any[]

    switch (reportType) {
      case 'tax_report':
        if (!filters.financial_year) {
          return NextResponse.json({ error: 'Financial year is required for tax report' }, { status: 400 })
        }
        
        const { report: taxReport, error: taxError } = await ReportingService.generateTaxReport(filters.financial_year)
        if (taxError) {
          return NextResponse.json({ error: taxError }, { status: 500 })
        }
        
        reportData = taxReport
        filename = `tax-report-${filters.financial_year}`
        headers = [
          'Transaction ID',
          'Date',
          'Type',
          'Product',
          'Quantity',
          'Price per Unit (₹)',
          'Total Amount (₹)',
          'Fees (₹)',
          'Holding Period (Days)',
          'Gain/Loss (₹)',
          'Gain Type'
        ]
        rows = taxReport.transactions.map(tx => [
          tx.id,
          new Date(tx.date).toLocaleDateString('en-IN'),
          tx.type.toUpperCase(),
          tx.product_name,
          tx.quantity,
          tx.price_per_unit,
          tx.total_amount,
          tx.fees,
          tx.holding_period_days || '',
          tx.gain_loss || '',
          tx.gain_type || ''
        ])
        
        // Add summary rows
        rows.push([])
        rows.push(['SUMMARY', '', '', '', '', '', '', '', '', '', ''])
        rows.push(['Total Purchases', '', '', '', '', '', taxReport.total_purchases, '', '', '', ''])
        rows.push(['Total Sales', '', '', '', '', '', taxReport.total_sales, '', '', '', ''])
        rows.push(['Short Term Gains', '', '', '', '', '', taxReport.short_term_gains, '', '', '', ''])
        rows.push(['Long Term Gains', '', '', '', '', '', taxReport.long_term_gains, '', '', '', ''])
        rows.push(['Total Fees', '', '', '', '', '', taxReport.total_fees, '', '', '', ''])
        rows.push(['Estimated Tax Liability', '', '', '', '', '', taxReport.total_tax_liability, '', '', '', ''])
        break

      case 'profit_loss':
        const { report: pnlReport, error: pnlError } = await ReportingService.generateProfitLossReport(filters)
        if (pnlError) {
          return NextResponse.json({ error: pnlError }, { status: 500 })
        }
        
        reportData = pnlReport
        filename = `profit-loss-report-${new Date().toISOString().split('T')[0]}`
        headers = ['Metric', 'Value']
        rows = [
          ['Period', pnlReport.period],
          ['Total Invested (₹)', pnlReport.total_invested.toLocaleString('en-IN')],
          ['Current Value (₹)', pnlReport.total_current_value.toLocaleString('en-IN')],
          ['Realized P&L (₹)', pnlReport.realized_pnl.toLocaleString('en-IN')],
          ['Unrealized P&L (₹)', pnlReport.unrealized_pnl.toLocaleString('en-IN')],
          ['Total P&L (₹)', pnlReport.total_pnl.toLocaleString('en-IN')],
          ['Total Fees (₹)', pnlReport.total_fees.toLocaleString('en-IN')],
          ['Net P&L (₹)', pnlReport.net_pnl.toLocaleString('en-IN')],
          ['ROI (%)', pnlReport.roi_percentage.toFixed(2) + '%'],
          ['Total Transactions', pnlReport.transactions_count],
          ['Winning Trades', pnlReport.winning_trades],
          ['Losing Trades', pnlReport.losing_trades],
          ['Win Rate (%)', pnlReport.win_rate.toFixed(2) + '%'],
          ['Best Performing Asset', pnlReport.best_performing_asset],
          ['Worst Performing Asset', pnlReport.worst_performing_asset]
        ]
        break

      case 'trade_summary':
        // Get trade summary data
        let query = supabase
          .from('trades')
          .select(`
            *,
            bullion_products (
              name,
              symbol,
              type
            )
          `)
          .order('created_at', { ascending: false })

        if (filters.date_from) query = query.gte('created_at', filters.date_from)
        if (filters.date_to) query = query.lte('created_at', filters.date_to)
        if (filters.status && filters.status !== 'all') query = query.eq('status', filters.status)
        if (filters.trade_type && filters.trade_type !== 'all') query = query.eq('type', filters.trade_type)
        if (filters.product_id) query = query.eq('product_id', filters.product_id)

        const { data: trades, error: tradesError } = await query
        if (tradesError) {
          return NextResponse.json({ error: tradesError.message }, { status: 500 })
        }

        filename = `trade-summary-${new Date().toISOString().split('T')[0]}`
        headers = [
          'Transaction ID',
          'Date',
          'Product',
          'Type',
          'Quantity',
          'Price per Unit (₹)',
          'Total Amount (₹)',
          'Fees (₹)',
          'Status',
          'Completed Date',
          'Notes'
        ]
        rows = trades.map(trade => [
          trade.id,
          new Date(trade.created_at).toLocaleDateString('en-IN'),
          trade.bullion_products?.name || 'Unknown',
          trade.type.toUpperCase(),
          trade.quantity,
          trade.price_per_unit,
          trade.total_amount,
          trade.fees || 0,
          trade.status.toUpperCase(),
          trade.completed_at ? new Date(trade.completed_at).toLocaleDateString('en-IN') : '',
          trade.notes || ''
        ])
        break

      default:
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 })
    }

    // Generate CSV content
    const csvContent = [
      headers.join(','),
      ...rows.map(row => 
        row.map(field => {
          const stringField = String(field || '')
          return stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')
            ? `"${stringField.replace(/"/g, '""')}"` 
            : stringField
        }).join(',')
      )
    ].join('\n')

    // Log the export activity
    await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: `export_${reportType}`,
        table_name: 'trades',
        new_values: {
          report_type: reportType,
          format,
          filters,
          rows_count: rows.length
        }
      })

    // Return appropriate format
    if (format === 'csv') {
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}.csv"`,
          'Cache-Control': 'no-cache'
        }
      })
    } else if (format === 'json') {
      return NextResponse.json({
        report_type: reportType,
        generated_at: new Date().toISOString(),
        data: reportData,
        summary: {
          total_rows: rows.length,
          filters_applied: filters
        }
      }, {
        headers: {
          'Content-Disposition': `attachment; filename="${filename}.json"`,
          'Cache-Control': 'no-cache'
        }
      })
    }

    return NextResponse.json({ error: 'Unsupported format' }, { status: 400 })

  } catch (error) {
    console.error('Report export API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
