import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const type = searchParams.get('type')
    const product_id = searchParams.get('product_id')
    const date_from = searchParams.get('date_from')
    const date_to = searchParams.get('date_to')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query
    let query = supabase
      .from('trades')
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit,
          current_price,
          image_url
        )
      `, { count: 'exact' })
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    if (type && type !== 'all') {
      query = query.eq('type', type)
    }

    if (product_id && product_id !== 'all') {
      query = query.eq('product_id', product_id)
    }

    if (date_from) {
      query = query.gte('created_at', date_from)
    }

    if (date_to) {
      query = query.lte('created_at', date_to)
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data: transactions, error, count } = await query

    if (error) {
      console.error('Error fetching transactions:', error)
      return NextResponse.json({ error: 'Failed to fetch transactions' }, { status: 500 })
    }

    return NextResponse.json({
      transactions: transactions || [],
      total: count || 0,
      page: Math.floor(offset / limit) + 1,
      limit,
      has_more: (count || 0) > offset + limit
    })
  } catch (error) {
    console.error('Error in transactions API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Get transaction statistics
export async function POST(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    if (action === 'stats') {
      // Get transaction statistics
      const { data: transactions, error } = await supabase
        .from('trades')
        .select('type, status, total_amount, fees')
        .eq('user_id', user.id)

      if (error) {
        console.error('Error fetching transaction stats:', error)
        return NextResponse.json({ error: 'Failed to fetch statistics' }, { status: 500 })
      }

      const stats = {
        total_transactions: transactions.length,
        total_volume: 0,
        total_fees: 0,
        buy_volume: 0,
        sell_volume: 0,
        completed_transactions: 0,
        pending_transactions: 0,
        cancelled_transactions: 0,
        profit_loss: 0
      }

      transactions.forEach(transaction => {
        stats.total_volume += Math.abs(transaction.total_amount)
        stats.total_fees += transaction.fees || 0

        if (transaction.type === 'buy') {
          stats.buy_volume += transaction.total_amount
        } else {
          stats.sell_volume += Math.abs(transaction.total_amount)
        }

        switch (transaction.status) {
          case 'completed':
            stats.completed_transactions++
            break
          case 'pending':
            stats.pending_transactions++
            break
          case 'cancelled':
            stats.cancelled_transactions++
            break
        }
      })

      // Calculate profit/loss (simplified - sell volume minus buy volume)
      stats.profit_loss = stats.sell_volume - stats.buy_volume

      return NextResponse.json({ stats })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    console.error('Error in transactions POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
