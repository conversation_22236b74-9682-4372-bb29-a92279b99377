import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    const transactionId = params.id

    // Get the transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('trades')
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit
        )
      `)
      .eq('id', transactionId)
      .eq('user_id', user.id)
      .single()

    if (transactionError || !transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 })
    }

    // Only generate receipts for completed transactions
    if (transaction.status !== 'completed') {
      return NextResponse.json({ error: 'Receipt can only be generated for completed transactions' }, { status: 400 })
    }

    // Check if receipt already exists
    const { data: existingReceipt } = await supabase
      .from('receipts')
      .select('*')
      .eq('transaction_id', transactionId)
      .single()

    if (existingReceipt) {
      return NextResponse.json({ 
        receipt: existingReceipt,
        message: 'Receipt already exists'
      })
    }

    // Generate receipt number
    const receiptNumber = `RCP-${Date.now()}-${transactionId.slice(-6).toUpperCase()}`

    // Calculate tax (assuming 18% GST on fees)
    const tax = (transaction.fees || 0) * 0.18

    // Create receipt record
    const receiptData = {
      transaction_id: transactionId,
      user_id: user.id,
      receipt_number: receiptNumber,
      amount: transaction.total_amount - (transaction.fees || 0),
      fees: transaction.fees || 0,
      tax: tax,
      total_amount: transaction.total_amount + tax,
      payment_method: 'bank_transfer', // Default for now
      status: 'generated' as const,
      generated_at: new Date().toISOString()
    }

    const { data: receipt, error: receiptError } = await supabase
      .from('receipts')
      .insert(receiptData)
      .select()
      .single()

    if (receiptError) {
      console.error('Error creating receipt:', receiptError)
      return NextResponse.json({ error: 'Failed to generate receipt' }, { status: 500 })
    }

    // Generate PDF receipt (simplified HTML version for now)
    const receiptHTML = generateReceiptHTML(transaction, receipt, user)
    
    // In a real implementation, you would use a PDF generation library like puppeteer
    // For now, we'll just return the receipt data
    
    return NextResponse.json({
      receipt,
      html: receiptHTML,
      message: 'Receipt generated successfully'
    })
  } catch (error) {
    console.error('Error in receipt generation API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

function generateReceiptHTML(transaction: any, receipt: any, user: any): string {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Receipt - ${receipt.receipt_number}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .receipt { max-width: 600px; margin: 0 auto; border: 1px solid #ddd; }
        .header { background: #f8f9fa; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .row { display: flex; justify-content: space-between; margin: 10px 0; }
        .total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #ddd; padding-top: 10px; }
        .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 0.9em; color: #666; }
      </style>
    </head>
    <body>
      <div class="receipt">
        <div class="header">
          <h1>BullionTrade</h1>
          <h2>Transaction Receipt</h2>
          <p>Receipt #: ${receipt.receipt_number}</p>
        </div>
        
        <div class="content">
          <h3>Transaction Details</h3>
          <div class="row">
            <span>Transaction ID:</span>
            <span>${transaction.id}</span>
          </div>
          <div class="row">
            <span>Date:</span>
            <span>${formatDate(transaction.created_at)}</span>
          </div>
          <div class="row">
            <span>Product:</span>
            <span>${transaction.bullion_products?.name}</span>
          </div>
          <div class="row">
            <span>Type:</span>
            <span>${transaction.type.toUpperCase()}</span>
          </div>
          <div class="row">
            <span>Quantity:</span>
            <span>${transaction.quantity} units</span>
          </div>
          <div class="row">
            <span>Price per Unit:</span>
            <span>${formatCurrency(transaction.price_per_unit)}</span>
          </div>
          
          <h3>Payment Summary</h3>
          <div class="row">
            <span>Subtotal:</span>
            <span>${formatCurrency(receipt.amount)}</span>
          </div>
          <div class="row">
            <span>Trading Fees:</span>
            <span>${formatCurrency(receipt.fees)}</span>
          </div>
          <div class="row">
            <span>Tax (GST):</span>
            <span>${formatCurrency(receipt.tax)}</span>
          </div>
          <div class="row total">
            <span>Total Amount:</span>
            <span>${formatCurrency(receipt.total_amount)}</span>
          </div>
          
          <h3>Customer Information</h3>
          <div class="row">
            <span>Email:</span>
            <span>${user.email}</span>
          </div>
          <div class="row">
            <span>User ID:</span>
            <span>${user.id}</span>
          </div>
        </div>
        
        <div class="footer">
          <p>Thank you for trading with BullionTrade!</p>
          <p>Generated on: ${formatDate(receipt.generated_at)}</p>
          <p>This is a computer-generated receipt and does not require a signature.</p>
        </div>
      </div>
    </body>
    </html>
  `
}
