import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const type = searchParams.get('type')
    const product_id = searchParams.get('product_id')
    const date_from = searchParams.get('date_from')
    const date_to = searchParams.get('date_to')

    // Build query
    let query = supabase
      .from('trades')
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    if (type && type !== 'all') {
      query = query.eq('type', type)
    }

    if (product_id && product_id !== 'all') {
      query = query.eq('product_id', product_id)
    }

    if (date_from) {
      query = query.gte('created_at', date_from)
    }

    if (date_to) {
      query = query.lte('created_at', date_to)
    }

    const { data: transactions, error } = await query

    if (error) {
      console.error('Error fetching transactions for export:', error)
      return NextResponse.json({ error: 'Failed to fetch transactions' }, { status: 500 })
    }

    // Generate CSV content
    const csvHeaders = [
      'Transaction ID',
      'Date',
      'Product',
      'Type',
      'Quantity',
      'Price per Unit',
      'Total Amount',
      'Fees',
      'Status',
      'Completed Date',
      'Notes'
    ]

    const csvRows = transactions.map(transaction => [
      transaction.id,
      new Date(transaction.created_at).toLocaleDateString('en-IN'),
      transaction.bullion_products?.name || 'Unknown',
      transaction.type.toUpperCase(),
      transaction.quantity,
      `₹${transaction.price_per_unit.toLocaleString('en-IN')}`,
      `₹${transaction.total_amount.toLocaleString('en-IN')}`,
      `₹${(transaction.fees || 0).toLocaleString('en-IN')}`,
      transaction.status.toUpperCase(),
      transaction.completed_at ? new Date(transaction.completed_at).toLocaleDateString('en-IN') : '',
      transaction.notes || ''
    ])

    // Convert to CSV format
    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => 
        row.map(field => 
          typeof field === 'string' && field.includes(',') 
            ? `"${field.replace(/"/g, '""')}"` 
            : field
        ).join(',')
      )
    ].join('\n')

    // Return CSV file
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="transactions-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    })
  } catch (error) {
    console.error('Error in export transactions API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
