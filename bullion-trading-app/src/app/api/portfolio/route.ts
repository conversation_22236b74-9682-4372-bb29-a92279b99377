import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get('include_stats') === 'true'

    // Get portfolio holdings
    const { data: portfolios, error: portfolioError } = await supabase
      .from('portfolios')
      .select(`
        *,
        bullion_products (
          id,
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit,
          current_price,
          image_url,
          description
        )
      `)
      .eq('user_id', user.id)
      .gt('quantity', 0)
      .order('current_value', { ascending: false })

    if (portfolioError) {
      console.error('Portfolio fetch error:', portfolioError)
      return NextResponse.json({ error: 'Failed to fetch portfolio' }, { status: 500 })
    }

    const holdings = (portfolios || []).map(portfolio => ({
      id: portfolio.id,
      product_id: portfolio.product_id,
      product_name: portfolio.bullion_products?.name || 'Unknown Product',
      product_symbol: portfolio.bullion_products?.symbol || '',
      metal_type: portfolio.bullion_products?.type || 'unknown',
      quantity: portfolio.quantity,
      average_buy_price: portfolio.average_buy_price,
      current_price: portfolio.bullion_products?.current_price || 0,
      total_invested: portfolio.total_invested,
      current_value: portfolio.current_value,
      profit_loss: portfolio.profit_loss,
      profit_loss_percentage: portfolio.profit_loss_percentage,
      weight: portfolio.bullion_products?.weight || 0,
      weight_unit: portfolio.bullion_products?.weight_unit || 'g',
      purity: portfolio.bullion_products?.purity || 0,
      last_updated: portfolio.last_updated,
      image_url: portfolio.bullion_products?.image_url,
      description: portfolio.bullion_products?.description
    }))

    let stats = null
    if (includeStats) {
      // Calculate portfolio statistics
      const totalValue = holdings.reduce((sum, h) => sum + h.current_value, 0)
      const totalInvested = holdings.reduce((sum, h) => sum + h.total_invested, 0)
      const totalPnL = totalValue - totalInvested
      const totalPnLPercent = totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0

      // Calculate diversification
      const diversification = { gold: 0, silver: 0, platinum: 0, palladium: 0 }
      holdings.forEach(holding => {
        const metalType = holding.metal_type.toLowerCase()
        if (metalType in diversification) {
          diversification[metalType as keyof typeof diversification] += holding.current_value
        }
      })

      // Convert to percentages
      if (totalValue > 0) {
        Object.keys(diversification).forEach(key => {
          diversification[key as keyof typeof diversification] = 
            (diversification[key as keyof typeof diversification] / totalValue) * 100
        })
      }

      // Find top and worst performers
      let topPerformer = null
      let worstPerformer = null

      if (holdings.length > 0) {
        topPerformer = holdings.reduce((best, current) => 
          current.profit_loss_percentage > best.profit_loss_percentage ? current : best
        )
        worstPerformer = holdings.reduce((worst, current) => 
          current.profit_loss_percentage < worst.profit_loss_percentage ? current : worst
        )
      }

      stats = {
        total_value: totalValue,
        total_invested: totalInvested,
        total_pnl: totalPnL,
        total_pnl_percent: totalPnLPercent,
        day_change: 0, // Would need historical data
        day_change_percent: 0,
        holdings_count: holdings.length,
        diversification,
        top_performer: topPerformer ? {
          name: topPerformer.product_name,
          pnl_percent: topPerformer.profit_loss_percentage
        } : null,
        worst_performer: worstPerformer ? {
          name: worstPerformer.product_name,
          pnl_percent: worstPerformer.profit_loss_percentage
        } : null
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        holdings,
        stats
      }
    })

  } catch (error) {
    console.error('Portfolio API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    if (action === 'refresh_values') {
      // Trigger portfolio value recalculation
      // This would typically call a stored procedure or background job
      
      // For now, we'll update portfolio values based on current product prices
      const { data: portfolios, error: fetchError } = await supabase
        .from('portfolios')
        .select('id, product_id, quantity, total_invested')
        .eq('user_id', user.id)
        .gt('quantity', 0)

      if (fetchError) {
        return NextResponse.json({ error: 'Failed to fetch portfolios' }, { status: 500 })
      }

      // Update each portfolio position
      for (const portfolio of portfolios || []) {
        const { data: product, error: productError } = await supabase
          .from('bullion_products')
          .select('current_price')
          .eq('id', portfolio.product_id)
          .single()

        if (productError || !product) continue

        const currentValue = portfolio.quantity * product.current_price
        const profitLoss = currentValue - portfolio.total_invested
        const profitLossPercent = portfolio.total_invested > 0 
          ? (profitLoss / portfolio.total_invested) * 100 
          : 0

        await supabase
          .from('portfolios')
          .update({
            current_value: currentValue,
            profit_loss: profitLoss,
            profit_loss_percentage: profitLossPercent,
            last_updated: new Date().toISOString()
          })
          .eq('id', portfolio.id)
      }

      return NextResponse.json({
        success: true,
        message: 'Portfolio values refreshed successfully'
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('Portfolio POST API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
