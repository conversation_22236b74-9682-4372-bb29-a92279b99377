import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get portfolio data
    const { data: portfolios, error: portfolioError } = await supabase
      .from('portfolios')
      .select(`
        *,
        bullion_products (
          name,
          symbol,
          type,
          purity,
          weight,
          weight_unit,
          current_price
        )
      `)
      .eq('user_id', user.id)
      .gt('quantity', 0)
      .order('current_value', { ascending: false })

    if (portfolioError) {
      console.error('Portfolio export error:', portfolioError)
      return NextResponse.json({ error: 'Failed to fetch portfolio data' }, { status: 500 })
    }

    if (!portfolios || portfolios.length === 0) {
      return NextResponse.json({ error: 'No portfolio data to export' }, { status: 404 })
    }

    // Generate CSV content
    const headers = [
      'Product Name',
      'Symbol',
      'Metal Type',
      'Purity (%)',
      'Weight per Unit',
      'Weight Unit',
      'Quantity',
      'Average Buy Price (₹)',
      'Current Price (₹)',
      'Total Invested (₹)',
      'Current Value (₹)',
      'Profit/Loss (₹)',
      'Profit/Loss (%)',
      'Last Updated'
    ]

    const csvRows = [headers.join(',')]

    portfolios.forEach(portfolio => {
      const product = portfolio.bullion_products
      const row = [
        `"${product?.name || 'Unknown'}"`,
        `"${product?.symbol || ''}"`,
        `"${product?.type || ''}"`,
        product?.purity || 0,
        product?.weight || 0,
        `"${product?.weight_unit || ''}"`,
        portfolio.quantity,
        portfolio.average_buy_price,
        product?.current_price || 0,
        portfolio.total_invested,
        portfolio.current_value,
        portfolio.profit_loss,
        portfolio.profit_loss_percentage.toFixed(2),
        `"${new Date(portfolio.last_updated).toLocaleString('en-IN')}"`
      ]
      csvRows.push(row.join(','))
    })

    // Add summary row
    const totalInvested = portfolios.reduce((sum, p) => sum + p.total_invested, 0)
    const totalCurrentValue = portfolios.reduce((sum, p) => sum + p.current_value, 0)
    const totalPnL = totalCurrentValue - totalInvested
    const totalPnLPercent = totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0

    csvRows.push('') // Empty row
    csvRows.push([
      '"TOTAL"',
      '""',
      '""',
      '""',
      '""',
      '""',
      portfolios.reduce((sum, p) => sum + p.quantity, 0),
      '""',
      '""',
      totalInvested,
      totalCurrentValue,
      totalPnL,
      totalPnLPercent.toFixed(2),
      `"${new Date().toLocaleString('en-IN')}"`
    ].join(','))

    const csvContent = csvRows.join('\n')

    // Log the export activity
    await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: 'portfolio_export',
        table_name: 'portfolios',
        new_values: {
          export_count: portfolios.length,
          total_value: totalCurrentValue
        }
      })

    // Return CSV file
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="portfolio-${new Date().toISOString().split('T')[0]}.csv"`,
        'Cache-Control': 'no-cache'
      }
    })

  } catch (error) {
    console.error('Portfolio export API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
