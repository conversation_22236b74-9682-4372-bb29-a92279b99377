import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all user's portfolio positions
    const { data: portfolios, error: fetchError } = await supabase
      .from('portfolios')
      .select(`
        id,
        product_id,
        quantity,
        total_invested,
        bullion_products (
          current_price
        )
      `)
      .eq('user_id', user.id)
      .gt('quantity', 0)

    if (fetchError) {
      console.error('Error fetching portfolios:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch portfolio data' }, { status: 500 })
    }

    if (!portfolios || portfolios.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No portfolio positions to update',
        updated_count: 0
      })
    }

    let updatedCount = 0
    const updatePromises = portfolios.map(async (portfolio) => {
      try {
        const currentPrice = portfolio.bullion_products?.current_price || 0
        const currentValue = portfolio.quantity * currentPrice
        const profitLoss = currentValue - portfolio.total_invested
        const profitLossPercent = portfolio.total_invested > 0 
          ? (profitLoss / portfolio.total_invested) * 100 
          : 0

        const { error: updateError } = await supabase
          .from('portfolios')
          .update({
            current_value: currentValue,
            profit_loss: profitLoss,
            profit_loss_percentage: profitLossPercent,
            last_updated: new Date().toISOString()
          })
          .eq('id', portfolio.id)

        if (updateError) {
          console.error(`Error updating portfolio ${portfolio.id}:`, updateError)
          return false
        }

        return true
      } catch (error) {
        console.error(`Error processing portfolio ${portfolio.id}:`, error)
        return false
      }
    })

    const results = await Promise.all(updatePromises)
    updatedCount = results.filter(Boolean).length

    // Log the refresh activity
    await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: 'portfolio_refresh',
        table_name: 'portfolios',
        new_values: {
          updated_count: updatedCount,
          total_positions: portfolios.length
        }
      })

    return NextResponse.json({
      success: true,
      message: `Successfully refreshed ${updatedCount} portfolio positions`,
      updated_count: updatedCount,
      total_positions: portfolios.length
    })

  } catch (error) {
    console.error('Portfolio refresh API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
