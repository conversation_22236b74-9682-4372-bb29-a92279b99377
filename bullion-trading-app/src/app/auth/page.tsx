'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { LoginForm } from '@/components/auth/LoginForm'
import { SignUpForm } from '@/components/auth/SignUpForm'
import { OTPVerification } from '@/components/auth/OTPVerification'
import { ForgotPasswordForm } from '@/components/auth/ForgotPasswordForm'

type AuthView = 'login' | 'signup' | 'otp' | 'forgot-password'

export default function AuthPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const initialView = (searchParams.get('view') as AuthView) || 'login'
  
  const [currentView, setCurrentView] = useState<AuthView>(initialView)
  const [otpPhone, setOtpPhone] = useState('')
  const [otpType, setOtpType] = useState<'registration' | 'login' | 'password_reset' | 'phone_verification'>('registration')

  const handleToggleForm = () => {
    setCurrentView(currentView === 'login' ? 'signup' : 'login')
  }

  const handleOTPRequired = (phone: string, type: 'registration' | 'login' | 'password_reset' | 'phone_verification' = 'registration') => {
    setOtpPhone(phone)
    setOtpType(type)
    setCurrentView('otp')
  }

  const handleForgotPassword = () => {
    setCurrentView('forgot-password')
  }

  const handleBackToLogin = () => {
    setCurrentView('login')
  }

  const handleAuthSuccess = () => {
    // Redirect to dashboard or intended page
    const redirectTo = searchParams.get('redirect') || '/dashboard'
    router.push(redirectTo)
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'login':
        return (
          <LoginForm
            onToggleForm={handleToggleForm}
            onForgotPassword={handleForgotPassword}
          />
        )
      
      case 'signup':
        return (
          <SignUpForm
            onToggleForm={handleToggleForm}
            onOTPRequired={handleOTPRequired}
          />
        )
      
      case 'otp':
        return (
          <OTPVerification
            phone={otpPhone}
            type={otpType}
            onBack={handleBackToLogin}
            onSuccess={handleAuthSuccess}
          />
        )
      
      case 'forgot-password':
        return (
          <ForgotPasswordForm
            onBack={handleBackToLogin}
            onOTPRequired={handleOTPRequired}
          />
        )
      
      default:
        return (
          <LoginForm
            onToggleForm={handleToggleForm}
            onForgotPassword={handleForgotPassword}
          />
        )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-yellow-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo/Brand */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-600 text-white rounded-full mb-4">
            <svg
              className="w-8 h-8"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">BullionTrade</h1>
          <p className="text-gray-600 mt-2">Secure Precious Metals Trading</p>
        </div>

        {renderCurrentView()}

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>
            By continuing, you agree to our{' '}
            <a href="/terms" className="text-amber-600 hover:underline">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" className="text-amber-600 hover:underline">
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
