'use client'

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { PriceDashboard } from '@/components/prices/PriceDashboard'
import { MainLayout } from '@/components/layout/MainLayout'
import { User, Phone, Mail, Shield, TrendingUp, Wallet, BarChart3, ShoppingCart } from 'lucide-react'

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}

function DashboardContent() {
  const { user } = useAuth()

  return (
    <MainLayout>
      {/* Welcome Section */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-6 border border-amber-200">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.full_name || 'Trader'}!
          </h1>
          <p className="text-gray-600">
            Ready to trade precious metals? Check out the latest prices and market trends below.
          </p>
        </div>
      </div>

      {/* Live Prices Section */}
      <div className="mb-8">
        <PriceDashboard />
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button className="h-20 flex flex-col items-center justify-center gap-2 bg-amber-600 hover:bg-amber-700">
            <ShoppingCart className="h-6 w-6" />
            <span>Buy Bullion</span>
          </Button>
          <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
            <TrendingUp className="h-6 w-6" />
            <span>Sell Bullion</span>
          </Button>
          <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
            <Wallet className="h-6 w-6" />
            <span>View Portfolio</span>
          </Button>
          <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
            <BarChart3 className="h-6 w-6" />
            <span>Market Analysis</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* User Profile Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Profile Information</span>
                </CardTitle>
                <CardDescription>Your account details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{user?.email}</span>
                  {user?.is_verified ? (
                    <Badge variant="secondary" className="text-xs">Verified</Badge>
                  ) : (
                    <Badge variant="destructive" className="text-xs">Unverified</Badge>
                  )}
                </div>
                
                {user?.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{user.phone}</span>
                    {user.is_phone_verified ? (
                      <Badge variant="secondary" className="text-xs">Verified</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">Unverified</Badge>
                    )}
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">
                    2FA: {user?.two_factor_enabled ? 'Enabled' : 'Disabled'}
                  </span>
                  {user?.two_factor_enabled ? (
                    <Badge variant="secondary" className="text-xs">Secure</Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">Setup Recommended</Badge>
                  )}
                </div>

                <div className="pt-2">
                  <Badge 
                    variant={user?.status === 'active' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    Status: {user?.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Portfolio Summary Card */}
            <Card>
              <CardHeader>
                <CardTitle>Portfolio Summary</CardTitle>
                <CardDescription>Your bullion holdings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500">No holdings yet</p>
                  <Button className="mt-4" size="sm">
                    Start Trading
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions Card */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  View Market Prices
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  Place Order
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  Set Price Alert
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  View Transaction History
                </Button>
              </CardContent>
            </Card>

          </div>

          {/* Welcome Message */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Welcome to BullionTrade</CardTitle>
              <CardDescription>
                Your secure platform for precious metals trading
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-gray-600">
                  You've successfully logged into your BullionTrade account. Here you can:
                </p>
                <ul className="list-disc list-inside text-gray-600 mt-2 space-y-1">
                  <li>View real-time precious metals prices</li>
                  <li>Buy and sell gold, silver, platinum, and palladium</li>
                  <li>Track your portfolio performance</li>
                  <li>Set price alerts for your favorite metals</li>
                  <li>Manage your account settings and security</li>
                </ul>
                
                {!user?.is_verified && (
                  <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-yellow-800 text-sm">
                      <strong>Action Required:</strong> Please verify your email address to unlock all features.
                    </p>
                  </div>
                )}

                {user?.phone && !user?.is_phone_verified && (
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-blue-800 text-sm">
                      <strong>Recommended:</strong> Verify your phone number for enhanced security and notifications.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
