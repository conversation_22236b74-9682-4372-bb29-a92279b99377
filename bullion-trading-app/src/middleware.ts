import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { rateLimiters, getRateLimitInfo } from '@/lib/security/rateLimiter'
import { applySecurityHeaders, applyCORSHeaders, handlePreflightRequest } from '@/lib/security/headers'
import { ThreatDetector } from '@/lib/security/validation'
import { auditLogger, AuditEventType, RiskLevel } from '@/lib/security/auditLogger'

// Security configuration
const SECURITY_CONFIG = {
  enableRateLimit: true,
  enableThreatDetection: true,
  enableAuditLogging: true,
  blockSuspiciousRequests: true,
  maxRequestSize: 10 * 1024 * 1024, // 10MB
}

// Get client IP address
function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for')
  const realIP = req.headers.get('x-real-ip')
  const cfConnectingIP = req.headers.get('cf-connecting-ip')

  if (forwarded) return forwarded.split(',')[0].trim()
  if (realIP) return realIP
  if (cfConnectingIP) return cfConnectingIP
  return req.ip || 'unknown'
}

// Determine rate limiter based on path
function getRateLimiterKey(pathname: string): keyof typeof rateLimiters {
  if (pathname.startsWith('/api/auth/')) return 'auth'
  if (pathname.startsWith('/api/trading/')) return 'trading'
  if (pathname.startsWith('/api/auth/send-otp')) return 'otp'
  if (pathname.startsWith('/api/reports/')) return 'reports'
  if (pathname.startsWith('/api/prices/')) return 'prices'
  if (pathname.includes('/upload')) return 'uploads'
  return 'api'
}

export async function middleware(req: NextRequest) {
  const startTime = Date.now()
  const clientIP = getClientIP(req)
  const userAgent = req.headers.get('user-agent') || 'unknown'
  const { pathname } = req.nextUrl
  const origin = req.headers.get('origin')

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return handlePreflightRequest(origin || undefined)
  }

  // Check request size
  const contentLength = req.headers.get('content-length')
  if (contentLength && parseInt(contentLength) > SECURITY_CONFIG.maxRequestSize) {
    await auditLogger.logSecurityEvent(
      AuditEventType.SECURITY_VIOLATION,
      'Request size exceeds limit',
      clientIP,
      undefined,
      RiskLevel.MEDIUM,
      { content_length: contentLength, max_size: SECURITY_CONFIG.maxRequestSize }
    )

    const response = NextResponse.json(
      { error: 'Request too large' },
      { status: 413 }
    )
    return applySecurityHeaders(applyCORSHeaders(response, origin))
  }

  // Rate limiting
  if (SECURITY_CONFIG.enableRateLimit) {
    const rateLimiterKey = getRateLimiterKey(pathname)
    const rateLimitResult = getRateLimitInfo(rateLimiterKey, req)

    if (!rateLimitResult.allowed) {
      await auditLogger.logSecurityEvent(
        AuditEventType.RATE_LIMIT_EXCEEDED,
        `Rate limit exceeded for ${pathname}`,
        clientIP,
        undefined,
        RiskLevel.MEDIUM,
        {
          path: pathname,
          rate_limiter: rateLimiterKey,
          total_hits: rateLimitResult.totalHits,
          limit: rateLimitResult.totalHits + rateLimitResult.remaining
        }
      )

      const response = NextResponse.json(
        {
          error: 'Too many requests',
          retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: rateLimitResult.headers
        }
      )
      return applySecurityHeaders(applyCORSHeaders(response, origin))
    }
  }

  let res = NextResponse.next({
    request: {
      headers: req.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          req.cookies.set({
            name,
            value,
            ...options,
          })
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          })
          res.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          })
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          })
          res.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Threat detection on request body and query parameters
  if (SECURITY_CONFIG.enableThreatDetection) {
    const queryString = req.nextUrl.search
    const referer = req.headers.get('referer') || ''

    // Check for threats in URL and headers
    const urlAnalysis = ThreatDetector.analyzeInput(pathname + queryString)
    const refererAnalysis = ThreatDetector.analyzeInput(referer)

    if (!urlAnalysis.safe || !refererAnalysis.safe) {
      const threats = [...urlAnalysis.threats, ...refererAnalysis.threats]

      await auditLogger.logSecurityEvent(
        AuditEventType.MALICIOUS_REQUEST,
        `Potential security threat detected: ${threats.join(', ')}`,
        clientIP,
        undefined,
        RiskLevel.HIGH,
        {
          threats,
          url: pathname + queryString,
          referer,
          user_agent: userAgent
        }
      )

      if (SECURITY_CONFIG.blockSuspiciousRequests) {
        const response = NextResponse.json(
          { error: 'Request blocked for security reasons' },
          { status: 403 }
        )
        return applySecurityHeaders(applyCORSHeaders(response, origin))
      }
    }
  }

  // Refresh session if expired - required for Server Components
  const { data: { session } } = await supabase.auth.getSession()

  // Define protected routes
  const protectedRoutes = [
    '/dashboard',
    '/portfolio',
    '/trading',
    '/profile',
    '/settings',
    '/api/trading',
    '/api/portfolio',
    '/api/user',
    '/api/orders',
    '/api/transactions',
    '/api/reports'
  ]

  // Define public routes that authenticated users shouldn't access
  const publicOnlyRoutes = ['/auth']

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  // Check if the current path is a public-only route
  const isPublicOnlyRoute = publicOnlyRoutes.some(route =>
    pathname.startsWith(route)
  )

  // Handle protected routes
  if (isProtectedRoute && !session) {
    // Log unauthorized access attempt
    if (SECURITY_CONFIG.enableAuditLogging) {
      await auditLogger.logSecurityEvent(
        AuditEventType.UNAUTHORIZED_ACCESS,
        `Unauthorized access attempt to ${pathname}`,
        clientIP,
        undefined,
        RiskLevel.MEDIUM,
        { path: pathname, user_agent: userAgent }
      )
    }

    const redirectUrl = new URL('/auth', req.url)
    redirectUrl.searchParams.set('redirect', pathname)
    const response = NextResponse.redirect(redirectUrl)
    return applySecurityHeaders(applyCORSHeaders(response, origin))
  }

  // Handle public-only routes (redirect authenticated users)
  if (isPublicOnlyRoute && session) {
    const redirectTo = req.nextUrl.searchParams.get('redirect') || '/dashboard'
    const response = NextResponse.redirect(new URL(redirectTo, req.url))
    return applySecurityHeaders(applyCORSHeaders(response, origin))
  }

  // Handle API routes authentication
  if (pathname.startsWith('/api/') && isProtectedRoute) {
    if (!session) {
      // Log API unauthorized access
      if (SECURITY_CONFIG.enableAuditLogging) {
        await auditLogger.logSecurityEvent(
          AuditEventType.UNAUTHORIZED_ACCESS,
          `Unauthorized API access attempt to ${pathname}`,
          clientIP,
          undefined,
          RiskLevel.HIGH,
          { path: pathname, method: req.method, user_agent: userAgent }
        )
      }

      const response = NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
      return applySecurityHeaders(applyCORSHeaders(response, origin))
    }

    // Add user info to headers for API routes
    const requestHeaders = new Headers(req.headers)
    requestHeaders.set('x-user-id', session.user.id)
    requestHeaders.set('x-user-email', session.user.email || '')
    requestHeaders.set('x-client-ip', clientIP)
    requestHeaders.set('x-request-start-time', startTime.toString())

    const response = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    })

    return applySecurityHeaders(applyCORSHeaders(response, origin))
  }

  // Apply security headers to all responses
  res = applySecurityHeaders(applyCORSHeaders(res, origin))

  // Add rate limit headers to response
  if (SECURITY_CONFIG.enableRateLimit) {
    const rateLimiterKey = getRateLimiterKey(pathname)
    const rateLimitResult = getRateLimitInfo(rateLimiterKey, req)

    Object.entries(rateLimitResult.headers).forEach(([key, value]) => {
      res.headers.set(key, value)
    })
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
