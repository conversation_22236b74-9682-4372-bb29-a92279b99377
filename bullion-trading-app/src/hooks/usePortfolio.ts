'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { PortfolioService, PortfolioHolding, PortfolioStats, PortfolioPerformance } from '@/lib/services/portfolioService'
import { supabase } from '@/lib/supabase/client'

export interface UsePortfolioOptions {
  autoRefresh?: boolean
  refreshInterval?: number
}

export interface UsePortfolioReturn {
  holdings: PortfolioHolding[]
  stats: PortfolioStats | null
  loading: boolean
  error: string | null
  refreshPortfolio: () => Promise<void>
  refreshValues: () => Promise<void>
  exportPortfolio: () => Promise<void>
}

export function usePortfolio(options: UsePortfolioOptions = {}): UsePortfolioReturn {
  const { autoRefresh = true, refreshInterval = 30000 } = options

  const [holdings, setHoldings] = useState<PortfolioHolding[]>([])
  const [stats, setStats] = useState<PortfolioStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const subscriptionRef = useRef<any>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Load portfolio data
  const loadPortfolio = useCallback(async () => {
    try {
      setError(null)
      
      const [holdingsResult, statsResult] = await Promise.all([
        PortfolioService.getPortfolio(),
        PortfolioService.getPortfolioStats()
      ])

      if (holdingsResult.error) {
        setError(holdingsResult.error)
        return
      }

      if (statsResult.error) {
        setError(statsResult.error)
        return
      }

      setHoldings(holdingsResult.holdings)
      setStats(statsResult.stats)
    } catch (err) {
      console.error('Error loading portfolio:', err)
      setError('Failed to load portfolio')
    } finally {
      setLoading(false)
    }
  }, [])

  // Refresh portfolio data
  const refreshPortfolio = useCallback(async () => {
    setLoading(true)
    await loadPortfolio()
  }, [loadPortfolio])

  // Refresh portfolio values (recalculate based on current prices)
  const refreshValues = useCallback(async () => {
    try {
      setError(null)
      const { success, error: refreshError } = await PortfolioService.refreshPortfolioValues()
      
      if (refreshError) {
        setError(refreshError)
        return
      }

      if (success) {
        // Reload portfolio after refresh
        await loadPortfolio()
      }
    } catch (err) {
      console.error('Error refreshing portfolio values:', err)
      setError('Failed to refresh portfolio values')
    }
  }, [loadPortfolio])

  // Export portfolio
  const exportPortfolio = useCallback(async () => {
    try {
      setError(null)
      const { url, error: exportError } = await PortfolioService.exportPortfolio()
      
      if (exportError) {
        setError(exportError)
        return
      }

      // Download the file
      const link = document.createElement('a')
      link.href = url
      link.download = `portfolio-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up the URL
      window.URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Error exporting portfolio:', err)
      setError('Failed to export portfolio')
    }
  }, [])

  // Set up real-time subscriptions
  useEffect(() => {
    if (autoRefresh) {
      // Subscribe to portfolio changes
      subscriptionRef.current = supabase
        .channel('portfolio')
        .on('postgres_changes', { 
          event: '*', 
          schema: 'public', 
          table: 'portfolios' 
        }, () => {
          loadPortfolio()
        })
        .on('postgres_changes', { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'bullion_products' 
        }, () => {
          // Price updates affect portfolio values
          loadPortfolio()
        })
        .subscribe()

      // Set up periodic refresh
      if (refreshInterval > 0) {
        intervalRef.current = setInterval(() => {
          loadPortfolio()
        }, refreshInterval)
      }
    }

    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current)
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [autoRefresh, refreshInterval, loadPortfolio])

  // Initial load
  useEffect(() => {
    loadPortfolio()
  }, [loadPortfolio])

  return {
    holdings,
    stats,
    loading,
    error,
    refreshPortfolio,
    refreshValues,
    exportPortfolio
  }
}

// Hook for portfolio performance data
export function usePortfolioPerformance(period: PortfolioPerformance['period'] = '1M') {
  const [performance, setPerformance] = useState<PortfolioPerformance | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadPerformance = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const { performance: performanceData, error: performanceError } = 
        await PortfolioService.getPortfolioPerformance(period)

      if (performanceError) {
        setError(performanceError)
        return
      }

      setPerformance(performanceData)
    } catch (err) {
      console.error('Error loading portfolio performance:', err)
      setError('Failed to load portfolio performance')
    } finally {
      setLoading(false)
    }
  }, [period])

  useEffect(() => {
    loadPerformance()
  }, [loadPerformance])

  return {
    performance,
    loading,
    error,
    refreshPerformance: loadPerformance
  }
}

// Hook for individual holding details
export function useHolding(productId: string) {
  const [holding, setHolding] = useState<PortfolioHolding | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadHolding = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const { holdings, error: holdingsError } = await PortfolioService.getPortfolio()

      if (holdingsError) {
        setError(holdingsError)
        return
      }

      const foundHolding = holdings.find(h => h.product_id === productId)
      setHolding(foundHolding || null)

      if (!foundHolding) {
        setError('Holding not found')
      }
    } catch (err) {
      console.error('Error loading holding:', err)
      setError('Failed to load holding')
    } finally {
      setLoading(false)
    }
  }, [productId])

  useEffect(() => {
    if (productId) {
      loadHolding()
    }
  }, [productId, loadHolding])

  return {
    holding,
    loading,
    error,
    refreshHolding: loadHolding
  }
}
