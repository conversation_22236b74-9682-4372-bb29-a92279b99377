'use client'

import { useState, useCallback } from 'react'
import { 
  ReportingService, 
  ReportType, 
  ReportFormat, 
  ReportFilters, 
  TaxReportData, 
  ProfitLossReport 
} from '@/lib/services/reportingService'

export interface UseReportsOptions {
  autoRefresh?: boolean
}

export function useReports(options: UseReportsOptions = {}) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [taxReport, setTaxReport] = useState<TaxReportData | null>(null)
  const [pnlReport, setPnlReport] = useState<ProfitLossReport | null>(null)

  // Generate tax report
  const generateTaxReport = useCallback(async (financialYear: string) => {
    setLoading(true)
    setError(null)

    try {
      const { report, error: reportError } = await ReportingService.generateTaxReport(financialYear)
      
      if (reportError) {
        setError(reportError)
        return { success: false, error: reportError }
      }

      setTaxReport(report)
      return { success: true, data: report }
    } catch (err) {
      const errorMessage = 'Failed to generate tax report'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  // Generate P&L report
  const generatePnLReport = useCallback(async (filters: ReportFilters) => {
    setLoading(true)
    setError(null)

    try {
      const { report, error: reportError } = await ReportingService.generateProfitLossReport(filters)
      
      if (reportError) {
        setError(reportError)
        return { success: false, error: reportError }
      }

      setPnlReport(report)
      return { success: true, data: report }
    } catch (err) {
      const errorMessage = 'Failed to generate P&L report'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  // Export report
  const exportReport = useCallback(async (
    reportType: ReportType,
    format: ReportFormat,
    filters: ReportFilters
  ) => {
    setLoading(true)
    setError(null)

    try {
      const { url, error: exportError } = await ReportingService.exportReport(reportType, format, filters)
      
      if (exportError) {
        setError(exportError)
        return { success: false, error: exportError }
      }

      // Trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = `${reportType}-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up the URL
      window.URL.revokeObjectURL(url)

      return { success: true, url }
    } catch (err) {
      const errorMessage = 'Failed to export report'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Clear reports
  const clearReports = useCallback(() => {
    setTaxReport(null)
    setPnlReport(null)
    setError(null)
  }, [])

  return {
    // State
    loading,
    error,
    taxReport,
    pnlReport,

    // Actions
    generateTaxReport,
    generatePnLReport,
    exportReport,
    clearError,
    clearReports
  }
}

// Hook for tax report calculations
export function useTaxCalculations() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const calculateTaxLiability = useCallback((taxReport: TaxReportData) => {
    try {
      // Indian tax rates for precious metals (simplified)
      const shortTermTaxRate = 0.30 // 30% (as per income tax slab)
      const longTermTaxRate = 0.20 // 20% with indexation benefit
      
      const shortTermTax = Math.max(0, taxReport.short_term_gains * shortTermTaxRate)
      const longTermTax = Math.max(0, taxReport.long_term_gains * longTermTaxRate)
      
      return {
        short_term_tax: shortTermTax,
        long_term_tax: longTermTax,
        total_tax: shortTermTax + longTermTax,
        effective_tax_rate: taxReport.total_purchases > 0 
          ? ((shortTermTax + longTermTax) / taxReport.total_purchases) * 100 
          : 0
      }
    } catch (err) {
      console.error('Error calculating tax liability:', err)
      return {
        short_term_tax: 0,
        long_term_tax: 0,
        total_tax: 0,
        effective_tax_rate: 0
      }
    }
  }, [])

  const getHoldingPeriodClassification = useCallback((days: number) => {
    // For gold and silver in India, long-term is > 3 years (1095 days)
    return days > 1095 ? 'long_term' : 'short_term'
  }, [])

  const calculateIndexationBenefit = useCallback((
    purchasePrice: number,
    salePrice: number,
    purchaseYear: number,
    saleYear: number
  ) => {
    // Simplified indexation calculation
    // In reality, you would use Cost Inflation Index (CII) from Income Tax Department
    const inflationRate = 0.05 // 5% annual inflation assumption
    const years = saleYear - purchaseYear
    const indexedCost = purchasePrice * Math.pow(1 + inflationRate, years)
    
    return {
      indexed_cost: indexedCost,
      indexation_benefit: indexedCost - purchasePrice,
      taxable_gain: Math.max(0, salePrice - indexedCost)
    }
  }, [])

  return {
    loading,
    error,
    calculateTaxLiability,
    getHoldingPeriodClassification,
    calculateIndexationBenefit
  }
}

// Hook for performance analytics
export function usePerformanceAnalytics() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const calculateSharpeRatio = useCallback((returns: number[], riskFreeRate: number = 0.06) => {
    if (returns.length === 0) return 0
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
    const volatility = Math.sqrt(variance)
    
    return volatility > 0 ? (avgReturn - riskFreeRate) / volatility : 0
  }, [])

  const calculateMaxDrawdown = useCallback((values: number[]) => {
    if (values.length === 0) return 0
    
    let maxDrawdown = 0
    let peak = values[0]
    
    for (let i = 1; i < values.length; i++) {
      if (values[i] > peak) {
        peak = values[i]
      } else {
        const drawdown = (peak - values[i]) / peak
        maxDrawdown = Math.max(maxDrawdown, drawdown)
      }
    }
    
    return maxDrawdown
  }, [])

  const calculateVolatility = useCallback((returns: number[]) => {
    if (returns.length === 0) return 0
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
    
    return Math.sqrt(variance)
  }, [])

  const calculateAnnualizedReturn = useCallback((
    startValue: number,
    endValue: number,
    days: number
  ) => {
    if (startValue <= 0 || days <= 0) return 0
    
    const totalReturn = (endValue - startValue) / startValue
    const years = days / 365.25
    
    return Math.pow(1 + totalReturn, 1 / years) - 1
  }, [])

  return {
    loading,
    error,
    calculateSharpeRatio,
    calculateMaxDrawdown,
    calculateVolatility,
    calculateAnnualizedReturn
  }
}
