'use client'

import { useState, useEffect, useCallback } from 'react'
import { OrderService, Order, OrderFilters, CreateOrderRequest } from '@/lib/services/orderService'

interface UseOrdersReturn {
  orders: Order[]
  loading: boolean
  error: string | null
  stats: {
    total_orders: number
    pending_orders: number
    completed_orders: number
    cancelled_orders: number
    total_volume: number
    total_fees: number
  } | null
  createOrder: (orderData: CreateOrderRequest) => Promise<{ success: boolean; order?: Order; error?: string }>
  cancelOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>
  executeOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>
  deleteOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>
  refreshOrders: () => Promise<void>
  loadMore: () => Promise<void>
  hasMore: boolean
}

export function useOrders(filters: OrderFilters = {}): UseOrdersReturn {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState<any>(null)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)

  const limit = filters.limit || 20

  // Load orders
  const loadOrders = useCallback(async (reset = false) => {
    try {
      setLoading(true)
      setError(null)

      const currentOffset = reset ? 0 : offset
      const { orders: newOrders, error: fetchError } = await OrderService.getOrders({
        ...filters,
        limit,
        offset: currentOffset
      })

      if (fetchError) {
        setError(fetchError)
        return
      }

      if (reset) {
        setOrders(newOrders)
        setOffset(newOrders.length)
      } else {
        setOrders(prev => [...prev, ...newOrders])
        setOffset(prev => prev + newOrders.length)
      }

      setHasMore(newOrders.length === limit)
    } catch (err) {
      setError('Failed to load orders')
      console.error('Error loading orders:', err)
    } finally {
      setLoading(false)
    }
  }, [filters, limit, offset])

  // Load order statistics
  const loadStats = useCallback(async () => {
    try {
      const { stats: orderStats, error: statsError } = await OrderService.getOrderStats()
      
      if (statsError) {
        console.error('Error loading order stats:', statsError)
        return
      }

      setStats(orderStats)
    } catch (err) {
      console.error('Error loading order stats:', err)
    }
  }, [])

  // Initial load
  useEffect(() => {
    loadOrders(true)
    loadStats()
  }, [filters.status, filters.type, filters.product_id])

  // Refresh orders
  const refreshOrders = useCallback(async () => {
    await loadOrders(true)
    await loadStats()
  }, [loadOrders, loadStats])

  // Load more orders
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return
    await loadOrders(false)
  }, [hasMore, loading, loadOrders])

  // Create order
  const createOrder = useCallback(async (orderData: CreateOrderRequest) => {
    try {
      setError(null)

      // Validate order data
      const validation = OrderService.validateOrder(orderData)
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        }
      }

      const { order, error: createError } = await OrderService.createOrder(orderData)

      if (createError) {
        return {
          success: false,
          error: createError
        }
      }

      // Add new order to the beginning of the list
      setOrders(prev => [order, ...prev])
      
      // Refresh stats
      await loadStats()

      return {
        success: true,
        order
      }
    } catch (err) {
      console.error('Error creating order:', err)
      return {
        success: false,
        error: 'Failed to create order'
      }
    }
  }, [loadStats])

  // Cancel order
  const cancelOrder = useCallback(async (orderId: string) => {
    try {
      setError(null)

      const { order, error: cancelError } = await OrderService.cancelOrder(orderId)

      if (cancelError) {
        return {
          success: false,
          error: cancelError
        }
      }

      // Update order in the list
      setOrders(prev => prev.map(o => o.id === orderId ? order : o))
      
      // Refresh stats
      await loadStats()

      return { success: true }
    } catch (err) {
      console.error('Error cancelling order:', err)
      return {
        success: false,
        error: 'Failed to cancel order'
      }
    }
  }, [loadStats])

  // Execute order
  const executeOrder = useCallback(async (orderId: string) => {
    try {
      setError(null)

      const { order, error: executeError } = await OrderService.executeOrder(orderId)

      if (executeError) {
        return {
          success: false,
          error: executeError
        }
      }

      // Update order in the list
      setOrders(prev => prev.map(o => o.id === orderId ? order : o))
      
      // Refresh stats
      await loadStats()

      return { success: true }
    } catch (err) {
      console.error('Error executing order:', err)
      return {
        success: false,
        error: 'Failed to execute order'
      }
    }
  }, [loadStats])

  // Delete order
  const deleteOrder = useCallback(async (orderId: string) => {
    try {
      setError(null)

      const { success, error: deleteError } = await OrderService.deleteOrder(orderId)

      if (!success) {
        return {
          success: false,
          error: deleteError
        }
      }

      // Remove order from the list
      setOrders(prev => prev.filter(o => o.id !== orderId))
      
      // Refresh stats
      await loadStats()

      return { success: true }
    } catch (err) {
      console.error('Error deleting order:', err)
      return {
        success: false,
        error: 'Failed to delete order'
      }
    }
  }, [loadStats])

  return {
    orders,
    loading,
    error,
    stats,
    createOrder,
    cancelOrder,
    executeOrder,
    deleteOrder,
    refreshOrders,
    loadMore,
    hasMore
  }
}

// Hook for a single order
export function useOrder(orderId: string) {
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadOrder = useCallback(async () => {
    if (!orderId) return

    try {
      setLoading(true)
      setError(null)

      const { order: fetchedOrder, error: fetchError } = await OrderService.getOrder(orderId)

      if (fetchError) {
        setError(fetchError)
        return
      }

      setOrder(fetchedOrder)
    } catch (err) {
      setError('Failed to load order')
      console.error('Error loading order:', err)
    } finally {
      setLoading(false)
    }
  }, [orderId])

  useEffect(() => {
    loadOrder()
  }, [loadOrder])

  const refreshOrder = useCallback(async () => {
    await loadOrder()
  }, [loadOrder])

  return {
    order,
    loading,
    error,
    refreshOrder
  }
}

// Hook for order calculations
export function useOrderCalculations() {
  const calculateTotal = useCallback((quantity: number, price: number, orderType: 'buy' | 'sell' = 'buy') => {
    return OrderService.calculateTotalAmount(quantity, price, orderType)
  }, [])

  const calculateFees = useCallback((amount: number, orderType: 'buy' | 'sell' = 'buy') => {
    return OrderService.calculateFees(amount, orderType)
  }, [])

  const validateOrder = useCallback((orderData: CreateOrderRequest) => {
    return OrderService.validateOrder(orderData)
  }, [])

  const formatOrder = useCallback((order: Order) => {
    return OrderService.formatOrder(order)
  }, [])

  return {
    calculateTotal,
    calculateFees,
    validateOrder,
    formatOrder
  }
}
