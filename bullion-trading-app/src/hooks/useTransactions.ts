'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { TransactionService, Transaction, TransactionFilters, TransactionStats } from '@/lib/services/transactionService'
import { supabase } from '@/lib/supabase/client'

export interface UseTransactionsOptions extends TransactionFilters {
  autoRefresh?: boolean
  refreshInterval?: number
}

export interface UseTransactionsReturn {
  transactions: Transaction[]
  loading: boolean
  error: string | null
  stats: TransactionStats | null
  total: number
  hasMore: boolean
  refreshTransactions: () => Promise<void>
  loadMore: () => Promise<void>
  generateReceipt: (transactionId: string) => Promise<{ success: boolean; error?: string }>
  downloadReceipt: (receiptId: string) => Promise<{ success: boolean; error?: string }>
  exportTransactions: () => Promise<{ success: boolean; error?: string }>
}

export function useTransactions(options: UseTransactionsOptions = {}): UseTransactionsReturn {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState<TransactionStats | null>(null)
  const [total, setTotal] = useState(0)
  const [offset, setOffset] = useState(0)
  const [hasMore, setHasMore] = useState(true)

  const {
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    limit = 20,
    ...filters
  } = options

  const intervalRef = useRef<NodeJS.Timeout>()
  const subscriptionRef = useRef<any>()

  // Load transactions
  const loadTransactions = useCallback(async (reset = false) => {
    try {
      setError(null)
      if (reset) {
        setLoading(true)
        setOffset(0)
      }

      const currentOffset = reset ? 0 : offset
      const { transactions: newTransactions, total: totalCount, error: fetchError } = 
        await TransactionService.getTransactions({
          ...filters,
          limit,
          offset: currentOffset
        })

      if (fetchError) {
        setError(fetchError)
        return
      }

      if (reset) {
        setTransactions(newTransactions)
      } else {
        setTransactions(prev => [...prev, ...newTransactions])
      }

      setTotal(totalCount)
      setHasMore(newTransactions.length === limit && (currentOffset + newTransactions.length) < totalCount)
      
      if (!reset) {
        setOffset(prev => prev + newTransactions.length)
      }
    } catch (err) {
      console.error('Error loading transactions:', err)
      setError('Failed to load transactions')
    } finally {
      setLoading(false)
    }
  }, [filters, limit, offset])

  // Load transaction statistics
  const loadStats = useCallback(async () => {
    try {
      const { stats: transactionStats, error: statsError } = await TransactionService.getTransactionStats()
      
      if (statsError) {
        console.error('Error loading transaction stats:', statsError)
        return
      }

      setStats(transactionStats)
    } catch (err) {
      console.error('Error loading transaction stats:', err)
    }
  }, [])

  // Refresh transactions
  const refreshTransactions = useCallback(async () => {
    await Promise.all([
      loadTransactions(true),
      loadStats()
    ])
  }, [loadTransactions, loadStats])

  // Load more transactions
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return
    await loadTransactions(false)
  }, [hasMore, loading, loadTransactions])

  // Generate receipt
  const generateReceipt = useCallback(async (transactionId: string) => {
    try {
      const { receipt, error: receiptError } = await TransactionService.generateReceipt(transactionId)
      
      if (receiptError) {
        return { success: false, error: receiptError }
      }

      return { success: true }
    } catch (err) {
      console.error('Error generating receipt:', err)
      return { success: false, error: 'Failed to generate receipt' }
    }
  }, [])

  // Download receipt
  const downloadReceipt = useCallback(async (receiptId: string) => {
    try {
      const { url, error: downloadError } = await TransactionService.downloadReceipt(receiptId)
      
      if (downloadError) {
        return { success: false, error: downloadError }
      }

      // Trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = `receipt-${receiptId}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up the URL
      window.URL.revokeObjectURL(url)

      return { success: true }
    } catch (err) {
      console.error('Error downloading receipt:', err)
      return { success: false, error: 'Failed to download receipt' }
    }
  }, [])

  // Export transactions
  const exportTransactions = useCallback(async () => {
    try {
      const { url, error: exportError } = await TransactionService.exportTransactions(filters)
      
      if (exportError) {
        return { success: false, error: exportError }
      }

      // Trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up the URL
      window.URL.revokeObjectURL(url)

      return { success: true }
    } catch (err) {
      console.error('Error exporting transactions:', err)
      return { success: false, error: 'Failed to export transactions' }
    }
  }, [filters])

  // Set up real-time subscription
  useEffect(() => {
    if (!supabase) return

    // Subscribe to transaction changes
    subscriptionRef.current = supabase
      .channel('transactions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'trades'
        },
        (payload) => {
          console.log('Transaction change:', payload)
          
          // Refresh transactions when changes occur
          refreshTransactions()
        }
      )
      .subscribe()

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe()
      }
    }
  }, [refreshTransactions])

  // Set up auto-refresh
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      intervalRef.current = setInterval(() => {
        refreshTransactions()
      }, refreshInterval)

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }
  }, [autoRefresh, refreshInterval, refreshTransactions])

  // Initial load
  useEffect(() => {
    refreshTransactions()
  }, []) // Only run on mount

  // Reload when filters change
  useEffect(() => {
    if (transactions.length > 0) { // Only if we've loaded before
      refreshTransactions()
    }
  }, [filters.status, filters.type, filters.product_id, filters.date_from, filters.date_to])

  return {
    transactions,
    loading,
    error,
    stats,
    total,
    hasMore,
    refreshTransactions,
    loadMore,
    generateReceipt,
    downloadReceipt,
    exportTransactions
  }
}

// Hook for single transaction
export function useTransaction(id: string) {
  const [transaction, setTransaction] = useState<Transaction | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refreshTransaction = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const { transaction: fetchedTransaction, error: fetchError } = 
        await TransactionService.getTransaction(id)

      if (fetchError) {
        setError(fetchError)
        return
      }

      setTransaction(fetchedTransaction)
    } catch (err) {
      console.error('Error loading transaction:', err)
      setError('Failed to load transaction')
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    if (id) {
      refreshTransaction()
    }
  }, [id, refreshTransaction])

  return {
    transaction,
    loading,
    error,
    refreshTransaction
  }
}
