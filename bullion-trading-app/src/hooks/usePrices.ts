'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { MetalRateData } from '@/lib/api/metal-rates'

export interface PriceUpdate {
  type: 'connected' | 'price_update' | 'initial_prices' | 'connection_status' | 'heartbeat'
  data?: MetalRateData[]
  connected?: boolean
  timestamp: string
}

export interface UsePricesReturn {
  prices: Record<string, MetalRateData>
  isConnected: boolean
  isLoading: boolean
  error: string | null
  lastUpdate: Date | null
  reconnect: () => void
  disconnect: () => void
}

export function usePrices(): UsePricesReturn {
  const [prices, setPrices] = useState<Record<string, MetalRateData>>({})
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  
  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const maxReconnectAttempts = 5

  const connect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
    }

    setError(null)
    setIsLoading(true)

    try {
      const eventSource = new EventSource('/api/prices/stream')
      eventSourceRef.current = eventSource

      eventSource.onopen = () => {
        console.log('[Prices SSE] Connected')
        setIsConnected(true)
        setIsLoading(false)
        setError(null)
        reconnectAttemptsRef.current = 0
      }

      eventSource.onmessage = (event) => {
        try {
          const update: PriceUpdate = JSON.parse(event.data)
          handlePriceUpdate(update)
        } catch (err) {
          console.error('[Prices SSE] Error parsing message:', err)
        }
      }

      eventSource.onerror = (event) => {
        console.error('[Prices SSE] Connection error:', event)
        setIsConnected(false)
        setIsLoading(false)
        
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000)
          reconnectAttemptsRef.current++
          
          setError(`Connection lost. Reconnecting in ${delay / 1000}s... (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, delay)
        } else {
          setError('Failed to connect to price stream. Please refresh the page.')
        }
      }
    } catch (err) {
      console.error('[Prices SSE] Failed to create EventSource:', err)
      setError('Failed to establish price connection')
      setIsLoading(false)
    }
  }, [])

  const handlePriceUpdate = useCallback((update: PriceUpdate) => {
    setLastUpdate(new Date(update.timestamp))

    switch (update.type) {
      case 'connected':
        console.log('[Prices SSE] Connection established')
        break

      case 'initial_prices':
      case 'price_update':
        if (update.data) {
          setPrices(prevPrices => {
            const newPrices = { ...prevPrices }
            update.data!.forEach(rate => {
              newPrices[rate.symbol] = rate
            })
            return newPrices
          })
          setIsLoading(false)
        }
        break

      case 'connection_status':
        if (update.connected !== undefined) {
          setIsConnected(update.connected)
        }
        break

      case 'heartbeat':
        // Connection is alive
        break

      default:
        console.log('[Prices SSE] Unknown update type:', update.type)
    }
  }, [])

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    setIsConnected(false)
    setError(null)
  }, [])

  const reconnect = useCallback(() => {
    disconnect()
    reconnectAttemptsRef.current = 0
    connect()
  }, [connect, disconnect])

  useEffect(() => {
    connect()

    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  return {
    prices,
    isConnected,
    isLoading,
    error,
    lastUpdate,
    reconnect,
    disconnect
  }
}

// Hook for getting a specific metal's price
export function useMetalPrice(symbol: string) {
  const { prices, isConnected, isLoading, error, lastUpdate } = usePrices()
  
  return {
    price: prices[symbol.toUpperCase()] || null,
    isConnected,
    isLoading,
    error,
    lastUpdate
  }
}

// Hook for getting multiple metals' prices
export function useMetalPrices(symbols: string[]) {
  const { prices, isConnected, isLoading, error, lastUpdate } = usePrices()
  
  const selectedPrices = symbols.reduce((acc, symbol) => {
    const upperSymbol = symbol.toUpperCase()
    if (prices[upperSymbol]) {
      acc[upperSymbol] = prices[upperSymbol]
    }
    return acc
  }, {} as Record<string, MetalRateData>)
  
  return {
    prices: selectedPrices,
    isConnected,
    isLoading,
    error,
    lastUpdate
  }
}
