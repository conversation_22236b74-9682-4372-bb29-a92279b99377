import { PriceData } from '@/types'

const API_KEY = process.env.METAL_RATES_API_KEY || 'your-api-key-here'
const API_URL = process.env.METAL_RATES_API_URL || 'https://metal-api.boogafantastic.workers.dev'

export interface MetalRateData {
  symbol: string
  name: string
  price: number
  currency: string
  timestamp: string
  change_24h?: number
  change_percentage_24h?: number
}

export interface MetalRatesSSEMessage {
  type: 'initial' | 'update' | 'heartbeat'
  data?: MetalRateData[]
  count?: number
}

export class MetalRatesService {
  /**
   * Fetch current metal rates (REST API fallback)
   */
  static async getCurrentRates(): Promise<MetalRateData[]> {
    try {
      const response = await fetch(`${API_URL}/${API_KEY}/rates`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.rates || []
    } catch (error) {
      console.error('Error fetching metal rates:', error)
      throw error
    }
  }

  /**
   * Get historical data for a specific metal
   */
  static async getHistoricalData(
    symbol: string,
    period: '1h' | '24h' | '7d' | '30d' | '1y' = '24h'
  ): Promise<PriceData[]> {
    try {
      const response = await fetch(
        `${API_URL}/${API_KEY}/historical/${symbol}?period=${period}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.historical || []
    } catch (error) {
      console.error('Error fetching historical data:', error)
      throw error
    }
  }
}

/**
 * Metal Rates SSE Client for real-time updates
 */
export class MetalRatesSSEClient {
  private apiKey: string
  private eventSource: EventSource | null = null
  private reconnectAttempts = 0
  private isConnected = false
  private options: {
    autoReconnect: boolean
    maxReconnectAttempts: number
    reconnectDelay: number
  }

  // Event handlers
  public onRatesUpdate: ((rates: MetalRateData[], type: 'initial' | 'update') => void) | null = null
  public onConnectionChange: ((isConnected: boolean) => void) | null = null
  public onMaxReconnectAttemptsReached: (() => void) | null = null
  public onError: ((error: Event) => void) | null = null

  constructor(
    apiKey: string = API_KEY,
    options: Partial<MetalRatesSSEClient['options']> = {}
  ) {
    this.apiKey = apiKey
    this.options = {
      autoReconnect: true,
      maxReconnectAttempts: 10,
      reconnectDelay: 1000,
      ...options,
    }

    this.connect()
  }

  private connect(): void {
    const sseUrl = `${API_URL}/${this.apiKey}/stream`

    try {
      this.eventSource = new EventSource(sseUrl)
      this.setupEventListeners()
    } catch (error) {
      console.error('[SSE] Failed to create EventSource:', error)
      this.scheduleReconnect()
    }
  }

  private setupEventListeners(): void {
    if (!this.eventSource) return

    this.eventSource.onopen = () => {
      console.log('[SSE] Connected successfully')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.onConnectionChange?.(true)
    }

    this.eventSource.addEventListener('data', (event) => {
      try {
        const message: MetalRatesSSEMessage = JSON.parse(event.data)
        this.handleDataMessage(message)
      } catch (error) {
        console.error('[SSE] Error parsing data message:', error)
      }
    })

    this.eventSource.addEventListener('heartbeat', (event) => {
      console.log('[SSE] Received heartbeat')
      // Heartbeat received - connection is alive
    })

    this.eventSource.onerror = (error) => {
      console.error('[SSE] Connection error:', error)
      this.isConnected = false
      this.onConnectionChange?.(false)
      this.onError?.(error)

      if (this.options.autoReconnect) {
        this.scheduleReconnect()
      }
    }
  }

  private handleDataMessage(message: MetalRatesSSEMessage): void {
    switch (message.type) {
      case 'initial':
        console.log(`[SSE] Received initial rates: ${message.count} items`)
        if (message.data) {
          this.onRatesUpdate?.(message.data, 'initial')
        }
        break

      case 'update':
        console.log(`[SSE] Received rate update: ${message.count} items`)
        if (message.data) {
          this.onRatesUpdate?.(message.data, 'update')
        }
        break

      default:
        console.log('[SSE] Unknown message type:', message.type)
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      console.error('[SSE] Max reconnection attempts reached')
      this.onMaxReconnectAttemptsReached?.()
      return
    }

    const delay = this.options.reconnectDelay * Math.pow(2, this.reconnectAttempts)
    this.reconnectAttempts++

    console.log(`[SSE] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`)

    setTimeout(() => {
      this.connect()
    }, delay)
  }

  public disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
      this.isConnected = false
      this.onConnectionChange?.(false)
    }
  }

  public getConnectionStatus(): boolean {
    return this.isConnected
  }
}
