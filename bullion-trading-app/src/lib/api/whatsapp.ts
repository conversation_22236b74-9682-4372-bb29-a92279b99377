import { WhatsAppMessage, WhatsAppResponse } from '@/types'
import { formatPhoneForWhatsApp } from '@/lib/utils'

const API_URL = process.env.WHATSAPP_API_URL!
const APP_KEY = process.env.WHATSAPP_APP_KEY!
const AUTH_KEY = process.env.WHATSAPP_AUTH_KEY!

export class WhatsAppService {
  /**
   * Send a text message via WhatsApp
   */
  static async sendTextMessage(
    recipient: string,
    message: string
  ): Promise<WhatsAppResponse> {
    const formData = new FormData()
    formData.append('appkey', APP_KEY)
    formData.append('authkey', AUTH_KEY)
    formData.append('to', formatPhoneForWhatsApp(recipient))
    formData.append('message', message)
    formData.append('sandbox', 'false')

    try {
      const response = await fetch(API_URL, {
        method: 'POST',
        body: formData,
      })

      const responseData = await response.json()

      if (response.ok && responseData.message_status === 'Success') {
        console.log('WhatsApp message sent successfully:', responseData)
        return responseData
      } else {
        console.error('WhatsApp API Error:', responseData)
        throw new Error(responseData.error || 'Failed to send WhatsApp message')
      }
    } catch (error) {
      console.error('WhatsApp API Error:', error)
      throw error
    }
  }

  /**
   * Send OTP via WhatsApp
   */
  static async sendOTP(phone: string, otp: string): Promise<WhatsAppResponse> {
    const message = `Your Bullion Trading App verification code is: ${otp}. This code will expire in 10 minutes. Do not share this code with anyone.`
    
    return this.sendTextMessage(phone, message)
  }

  /**
   * Send welcome message after successful registration
   */
  static async sendWelcomeMessage(
    phone: string,
    userName: string
  ): Promise<WhatsAppResponse> {
    const message = `Welcome to Bullion Trading App, ${userName}! 🎉\n\nYour account has been successfully created. You can now start trading gold, silver, platinum, and palladium.\n\nHappy Trading! 📈`
    
    return this.sendTextMessage(phone, message)
  }

  /**
   * Send trade confirmation message
   */
  static async sendTradeConfirmation(
    phone: string,
    tradeDetails: {
      type: 'buy' | 'sell'
      product: string
      quantity: number
      price: number
      total: number
    }
  ): Promise<WhatsAppResponse> {
    const { type, product, quantity, price, total } = tradeDetails
    const action = type === 'buy' ? 'purchased' : 'sold'
    
    const message = `Trade Confirmation ✅\n\nYou have successfully ${action}:\n• ${quantity} units of ${product}\n• Price: ₹${price.toLocaleString()} per unit\n• Total: ₹${total.toLocaleString()}\n\nThank you for trading with us!`
    
    return this.sendTextMessage(phone, message)
  }

  /**
   * Send price alert notification
   */
  static async sendPriceAlert(
    phone: string,
    alertDetails: {
      product: string
      currentPrice: number
      targetPrice: number
      condition: 'above' | 'below'
    }
  ): Promise<WhatsAppResponse> {
    const { product, currentPrice, targetPrice, condition } = alertDetails
    const conditionText = condition === 'above' ? 'risen above' : 'fallen below'
    
    const message = `Price Alert! 🚨\n\n${product} has ${conditionText} your target price of ₹${targetPrice.toLocaleString()}.\n\nCurrent Price: ₹${currentPrice.toLocaleString()}\n\nConsider reviewing your trading strategy.`
    
    return this.sendTextMessage(phone, message)
  }

  /**
   * Send password reset OTP
   */
  static async sendPasswordResetOTP(
    phone: string,
    otp: string
  ): Promise<WhatsAppResponse> {
    const message = `Your password reset code for Bullion Trading App is: ${otp}\n\nThis code will expire in 10 minutes. If you didn't request this, please ignore this message.`
    
    return this.sendTextMessage(phone, message)
  }

  /**
   * Send template message (for pre-approved templates)
   */
  static async sendTemplateMessage(
    recipient: string,
    templateId: string,
    variables?: Record<string, string>
  ): Promise<WhatsAppResponse> {
    const formData = new FormData()
    formData.append('appkey', APP_KEY)
    formData.append('authkey', AUTH_KEY)
    formData.append('to', formatPhoneForWhatsApp(recipient))
    formData.append('template_id', templateId)
    formData.append('sandbox', 'false')

    if (variables) {
      for (const key in variables) {
        if (Object.hasOwnProperty.call(variables, key)) {
          formData.append(`variables[${key}]`, variables[key])
        }
      }
    }

    try {
      const response = await fetch(API_URL, {
        method: 'POST',
        body: formData,
      })

      const responseData = await response.json()

      if (response.ok && responseData.message_status === 'Success') {
        console.log('WhatsApp template message sent successfully:', responseData)
        return responseData
      } else {
        console.error('WhatsApp Template API Error:', responseData)
        throw new Error(responseData.error || 'Failed to send WhatsApp template message')
      }
    } catch (error) {
      console.error('WhatsApp Template API Error:', error)
      throw error
    }
  }
}
