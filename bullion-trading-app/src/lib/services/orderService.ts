import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/database'

export type Order = Database['public']['Tables']['trades']['Row'] & {
  bullion_products?: Database['public']['Tables']['bullion_products']['Row']
}

export type OrderStatus = Database['public']['Enums']['trade_status']
export type OrderType = Database['public']['Enums']['trade_type']

export interface CreateOrderRequest {
  product_id: string
  type: OrderType
  quantity: number
  price_per_unit: number
  order_method?: 'market' | 'limit'
  limit_price?: number
  notes?: string
}

export interface OrderFilters {
  status?: OrderStatus | 'all'
  type?: OrderType | 'all'
  product_id?: string
  date_from?: string
  date_to?: string
  limit?: number
  offset?: number
}

export class OrderService {
  /**
   * Create a new order
   */
  static async createOrder(orderData: CreateOrderRequest): Promise<{ order: Order; error?: string }> {
    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      const result = await response.json()

      if (!response.ok) {
        return { order: null as any, error: result.error || 'Failed to create order' }
      }

      return { order: result.order }
    } catch (error) {
      console.error('Error creating order:', error)
      return { order: null as any, error: 'Network error occurred' }
    }
  }

  /**
   * Get user's orders with optional filters
   */
  static async getOrders(filters: OrderFilters = {}): Promise<{ orders: Order[]; error?: string }> {
    try {
      const params = new URLSearchParams()
      
      if (filters.status && filters.status !== 'all') {
        params.append('status', filters.status)
      }
      if (filters.limit) {
        params.append('limit', filters.limit.toString())
      }
      if (filters.offset) {
        params.append('offset', filters.offset.toString())
      }

      const response = await fetch(`/api/orders?${params.toString()}`)
      const result = await response.json()

      if (!response.ok) {
        return { orders: [], error: result.error || 'Failed to fetch orders' }
      }

      return { orders: result.orders }
    } catch (error) {
      console.error('Error fetching orders:', error)
      return { orders: [], error: 'Network error occurred' }
    }
  }

  /**
   * Get a specific order by ID
   */
  static async getOrder(orderId: string): Promise<{ order: Order; error?: string }> {
    try {
      const response = await fetch(`/api/orders/${orderId}`)
      const result = await response.json()

      if (!response.ok) {
        return { order: null as any, error: result.error || 'Failed to fetch order' }
      }

      return { order: result.order }
    } catch (error) {
      console.error('Error fetching order:', error)
      return { order: null as any, error: 'Network error occurred' }
    }
  }

  /**
   * Cancel an order
   */
  static async cancelOrder(orderId: string): Promise<{ order: Order; error?: string }> {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'cancel' }),
      })

      const result = await response.json()

      if (!response.ok) {
        return { order: null as any, error: result.error || 'Failed to cancel order' }
      }

      return { order: result.order }
    } catch (error) {
      console.error('Error cancelling order:', error)
      return { order: null as any, error: 'Network error occurred' }
    }
  }

  /**
   * Execute a pending order
   */
  static async executeOrder(orderId: string): Promise<{ order: Order; error?: string }> {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'execute' }),
      })

      const result = await response.json()

      if (!response.ok) {
        return { order: null as any, error: result.error || 'Failed to execute order' }
      }

      return { order: result.order }
    } catch (error) {
      console.error('Error executing order:', error)
      return { order: null as any, error: 'Network error occurred' }
    }
  }

  /**
   * Delete an order (only cancelled or failed orders)
   */
  static async deleteOrder(orderId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (!response.ok) {
        return { success: false, error: result.error || 'Failed to delete order' }
      }

      return { success: true }
    } catch (error) {
      console.error('Error deleting order:', error)
      return { success: false, error: 'Network error occurred' }
    }
  }

  /**
   * Get order statistics for dashboard
   */
  static async getOrderStats(): Promise<{
    stats: {
      total_orders: number
      pending_orders: number
      completed_orders: number
      cancelled_orders: number
      total_volume: number
      total_fees: number
    }
    error?: string
  }> {
    try {
      const { orders, error } = await this.getOrders({ limit: 1000 })
      
      if (error) {
        return { stats: null as any, error }
      }

      const stats = orders.reduce((acc, order) => {
        acc.total_orders++
        
        switch (order.status) {
          case 'pending':
            acc.pending_orders++
            break
          case 'completed':
            acc.completed_orders++
            acc.total_volume += order.total_amount
            acc.total_fees += order.fees || 0
            break
          case 'cancelled':
            acc.cancelled_orders++
            break
        }
        
        return acc
      }, {
        total_orders: 0,
        pending_orders: 0,
        completed_orders: 0,
        cancelled_orders: 0,
        total_volume: 0,
        total_fees: 0
      })

      return { stats }
    } catch (error) {
      console.error('Error calculating order stats:', error)
      return { stats: null as any, error: 'Failed to calculate statistics' }
    }
  }

  /**
   * Calculate order fees
   */
  static calculateFees(amount: number, orderType: OrderType = 'buy'): number {
    const feeRate = 0.01 // 1% fee
    return amount * feeRate
  }

  /**
   * Calculate total order amount including fees
   */
  static calculateTotalAmount(quantity: number, price: number, orderType: OrderType = 'buy'): {
    subtotal: number
    fees: number
    total: number
  } {
    const subtotal = quantity * price
    const fees = this.calculateFees(subtotal, orderType)
    const total = subtotal + (orderType === 'buy' ? fees : -fees)

    return { subtotal, fees, total }
  }

  /**
   * Validate order data
   */
  static validateOrder(orderData: CreateOrderRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!orderData.product_id) {
      errors.push('Product ID is required')
    }

    if (!orderData.type || !['buy', 'sell'].includes(orderData.type)) {
      errors.push('Valid order type (buy/sell) is required')
    }

    if (!orderData.quantity || orderData.quantity <= 0) {
      errors.push('Quantity must be greater than 0')
    }

    if (!orderData.price_per_unit || orderData.price_per_unit <= 0) {
      errors.push('Price per unit must be greater than 0')
    }

    if (orderData.order_method === 'limit' && (!orderData.limit_price || orderData.limit_price <= 0)) {
      errors.push('Limit price is required for limit orders')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Format order for display
   */
  static formatOrder(order: Order) {
    return {
      ...order,
      formatted_amount: new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(order.total_amount),
      formatted_price: new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(order.price_per_unit),
      formatted_fees: new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(order.fees || 0),
      formatted_date: new Date(order.created_at).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      status_color: this.getStatusColor(order.status),
      type_color: order.type === 'buy' ? 'text-green-600' : 'text-red-600'
    }
  }

  /**
   * Get color class for order status
   */
  static getStatusColor(status: OrderStatus): string {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'completed':
        return 'text-green-600 bg-green-100'
      case 'cancelled':
        return 'text-gray-600 bg-gray-100'
      case 'failed':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }
}
