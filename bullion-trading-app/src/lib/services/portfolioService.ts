import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/database'

export type Portfolio = Database['public']['Tables']['portfolios']['Row'] & {
  bullion_products?: Database['public']['Tables']['bullion_products']['Row']
}

export interface PortfolioHolding {
  id: string
  product_id: string
  product_name: string
  product_symbol: string
  metal_type: string
  quantity: number
  average_buy_price: number
  current_price: number
  total_invested: number
  current_value: number
  profit_loss: number
  profit_loss_percentage: number
  weight: number
  weight_unit: string
  purity: number
  last_updated: string
  image_url?: string
}

export interface PortfolioStats {
  total_value: number
  total_invested: number
  total_pnl: number
  total_pnl_percent: number
  day_change: number
  day_change_percent: number
  holdings_count: number
  diversification: {
    gold: number
    silver: number
    platinum: number
    palladium: number
  }
  top_performer: {
    name: string
    pnl_percent: number
  } | null
  worst_performer: {
    name: string
    pnl_percent: number
  } | null
}

export interface PortfolioPerformance {
  period: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL'
  start_value: number
  end_value: number
  change: number
  change_percent: number
  data_points: Array<{
    date: string
    value: number
  }>
}

export class PortfolioService {
  /**
   * Get user's portfolio holdings
   */
  static async getPortfolio(): Promise<{ holdings: PortfolioHolding[]; error?: string }> {
    try {
      const { data: portfolios, error } = await supabase
        .from('portfolios')
        .select(`
          *,
          bullion_products (
            id,
            name,
            symbol,
            type,
            purity,
            weight,
            weight_unit,
            current_price,
            image_url
          )
        `)
        .gt('quantity', 0)
        .order('current_value', { ascending: false })

      if (error) {
        console.error('Error fetching portfolio:', error)
        return { holdings: [], error: error.message }
      }

      const holdings: PortfolioHolding[] = (portfolios || []).map(portfolio => ({
        id: portfolio.id,
        product_id: portfolio.product_id,
        product_name: portfolio.bullion_products?.name || 'Unknown Product',
        product_symbol: portfolio.bullion_products?.symbol || '',
        metal_type: portfolio.bullion_products?.type || 'unknown',
        quantity: portfolio.quantity,
        average_buy_price: portfolio.average_buy_price,
        current_price: portfolio.bullion_products?.current_price || 0,
        total_invested: portfolio.total_invested,
        current_value: portfolio.current_value,
        profit_loss: portfolio.profit_loss,
        profit_loss_percentage: portfolio.profit_loss_percentage,
        weight: portfolio.bullion_products?.weight || 0,
        weight_unit: portfolio.bullion_products?.weight_unit || 'g',
        purity: portfolio.bullion_products?.purity || 0,
        last_updated: portfolio.last_updated,
        image_url: portfolio.bullion_products?.image_url
      }))

      return { holdings }
    } catch (error) {
      console.error('Error in getPortfolio:', error)
      return { holdings: [], error: 'Failed to fetch portfolio' }
    }
  }

  /**
   * Get portfolio statistics
   */
  static async getPortfolioStats(): Promise<{ stats: PortfolioStats; error?: string }> {
    try {
      const { holdings, error: holdingsError } = await this.getPortfolio()
      
      if (holdingsError) {
        return { stats: null as any, error: holdingsError }
      }

      const stats: PortfolioStats = {
        total_value: 0,
        total_invested: 0,
        total_pnl: 0,
        total_pnl_percent: 0,
        day_change: 0, // Would need historical data to calculate
        day_change_percent: 0,
        holdings_count: holdings.length,
        diversification: {
          gold: 0,
          silver: 0,
          platinum: 0,
          palladium: 0
        },
        top_performer: null,
        worst_performer: null
      }

      let topPerformer: { name: string; pnl_percent: number } | null = null
      let worstPerformer: { name: string; pnl_percent: number } | null = null

      holdings.forEach(holding => {
        stats.total_value += holding.current_value
        stats.total_invested += holding.total_invested

        // Track diversification by metal type
        const metalType = holding.metal_type.toLowerCase()
        if (metalType in stats.diversification) {
          stats.diversification[metalType as keyof typeof stats.diversification] += holding.current_value
        }

        // Track top and worst performers
        if (!topPerformer || holding.profit_loss_percentage > topPerformer.pnl_percent) {
          topPerformer = {
            name: holding.product_name,
            pnl_percent: holding.profit_loss_percentage
          }
        }

        if (!worstPerformer || holding.profit_loss_percentage < worstPerformer.pnl_percent) {
          worstPerformer = {
            name: holding.product_name,
            pnl_percent: holding.profit_loss_percentage
          }
        }
      })

      // Calculate total P&L
      stats.total_pnl = stats.total_value - stats.total_invested
      stats.total_pnl_percent = stats.total_invested > 0 
        ? (stats.total_pnl / stats.total_invested) * 100 
        : 0

      // Convert diversification to percentages
      if (stats.total_value > 0) {
        Object.keys(stats.diversification).forEach(key => {
          stats.diversification[key as keyof typeof stats.diversification] = 
            (stats.diversification[key as keyof typeof stats.diversification] / stats.total_value) * 100
        })
      }

      stats.top_performer = topPerformer
      stats.worst_performer = worstPerformer

      return { stats }
    } catch (error) {
      console.error('Error in getPortfolioStats:', error)
      return { stats: null as any, error: 'Failed to fetch portfolio statistics' }
    }
  }

  /**
   * Get portfolio performance over time
   */
  static async getPortfolioPerformance(period: PortfolioPerformance['period']): Promise<{
    performance: PortfolioPerformance;
    error?: string;
  }> {
    try {
      // This would require historical portfolio value data
      // For now, return mock data structure
      const performance: PortfolioPerformance = {
        period,
        start_value: 0,
        end_value: 0,
        change: 0,
        change_percent: 0,
        data_points: []
      }

      // In a real implementation, you would:
      // 1. Query historical portfolio values or calculate from trade history
      // 2. Generate data points for the chart
      // 3. Calculate performance metrics

      return { performance }
    } catch (error) {
      console.error('Error in getPortfolioPerformance:', error)
      return { performance: null as any, error: 'Failed to fetch portfolio performance' }
    }
  }

  /**
   * Refresh portfolio values (recalculate based on current prices)
   */
  static async refreshPortfolioValues(): Promise<{ success: boolean; error?: string }> {
    try {
      // This would trigger a recalculation of all portfolio values
      // In a real implementation, you might call a stored procedure or API endpoint
      
      const response = await fetch('/api/portfolio/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (!response.ok) {
        return { success: false, error: result.error || 'Failed to refresh portfolio' }
      }

      return { success: true }
    } catch (error) {
      console.error('Error refreshing portfolio:', error)
      return { success: false, error: 'Network error occurred' }
    }
  }

  /**
   * Export portfolio data
   */
  static async exportPortfolio(): Promise<{ url: string; error?: string }> {
    try {
      const response = await fetch('/api/portfolio/export', {
        method: 'GET',
      })

      if (!response.ok) {
        const result = await response.json()
        return { url: '', error: result.error || 'Failed to export portfolio' }
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      return { url }
    } catch (error) {
      console.error('Error exporting portfolio:', error)
      return { url: '', error: 'Network error occurred' }
    }
  }

  /**
   * Format holding for display
   */
  static formatHolding(holding: PortfolioHolding) {
    const formatPrice = (price: number) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price)
    }

    const formatPercent = (percent: number) => {
      return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`
    }

    return {
      ...holding,
      formatted_current_price: formatPrice(holding.current_price),
      formatted_average_price: formatPrice(holding.average_buy_price),
      formatted_current_value: formatPrice(holding.current_value),
      formatted_total_invested: formatPrice(holding.total_invested),
      formatted_profit_loss: formatPrice(holding.profit_loss),
      formatted_profit_loss_percent: formatPercent(holding.profit_loss_percentage),
      pnl_color: holding.profit_loss >= 0 ? 'text-green-600' : 'text-red-600',
      pnl_bg_color: holding.profit_loss >= 0 ? 'bg-green-100' : 'bg-red-100'
    }
  }

  /**
   * Get metal type color for UI
   */
  static getMetalColor(metalType: string): string {
    switch (metalType.toLowerCase()) {
      case 'gold':
        return 'bg-yellow-100 text-yellow-800'
      case 'silver':
        return 'bg-gray-100 text-gray-800'
      case 'platinum':
        return 'bg-blue-100 text-blue-800'
      case 'palladium':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
}
