import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/database'

export type ReportType = 'trade_summary' | 'tax_report' | 'profit_loss' | 'portfolio_performance' | 'annual_statement'
export type ReportFormat = 'csv' | 'pdf' | 'json'

export interface ReportFilters {
  date_from?: string
  date_to?: string
  product_id?: string
  trade_type?: 'buy' | 'sell' | 'all'
  status?: 'completed' | 'pending' | 'cancelled' | 'all'
  financial_year?: string
}

export interface TaxReportData {
  financial_year: string
  total_purchases: number
  total_sales: number
  short_term_gains: number
  long_term_gains: number
  total_fees: number
  total_tax_liability: number
  transactions: TaxTransaction[]
}

export interface TaxTransaction {
  id: string
  date: string
  type: 'buy' | 'sell'
  product_name: string
  quantity: number
  price_per_unit: number
  total_amount: number
  fees: number
  holding_period_days?: number
  gain_loss?: number
  gain_type?: 'short_term' | 'long_term'
}

export interface ProfitLossReport {
  period: string
  total_invested: number
  total_current_value: number
  realized_pnl: number
  unrealized_pnl: number
  total_pnl: number
  total_fees: number
  net_pnl: number
  roi_percentage: number
  transactions_count: number
  winning_trades: number
  losing_trades: number
  win_rate: number
  best_performing_asset: string
  worst_performing_asset: string
}

export interface PortfolioPerformanceReport {
  period: string
  starting_value: number
  ending_value: number
  total_return: number
  total_return_percentage: number
  annualized_return: number
  volatility: number
  sharpe_ratio: number
  max_drawdown: number
  asset_allocation: AssetAllocation[]
  monthly_performance: MonthlyPerformance[]
}

export interface AssetAllocation {
  asset_type: string
  value: number
  percentage: number
  count: number
}

export interface MonthlyPerformance {
  month: string
  starting_value: number
  ending_value: number
  return_amount: number
  return_percentage: number
  trades_count: number
}

export class ReportingService {
  /**
   * Generate comprehensive tax report for a financial year
   */
  static async generateTaxReport(financialYear: string): Promise<{ report: TaxReportData; error?: string }> {
    try {
      // Financial year in India runs from April 1 to March 31
      const startDate = `${financialYear}-04-01`
      const endDate = `${parseInt(financialYear) + 1}-03-31`

      const { data: transactions, error } = await supabase
        .from('trades')
        .select(`
          *,
          bullion_products (
            name,
            symbol,
            type
          )
        `)
        .eq('status', 'completed')
        .gte('completed_at', startDate)
        .lte('completed_at', endDate)
        .order('completed_at', { ascending: true })

      if (error) {
        return { report: null as any, error: error.message }
      }

      // Process transactions for tax calculations
      const taxTransactions: TaxTransaction[] = []
      let totalPurchases = 0
      let totalSales = 0
      let shortTermGains = 0
      let longTermGains = 0
      let totalFees = 0

      // Group buy and sell transactions for FIFO calculation
      const holdings: { [productId: string]: Array<{ quantity: number; price: number; date: string }> } = {}

      for (const transaction of transactions) {
        const taxTransaction: TaxTransaction = {
          id: transaction.id,
          date: transaction.completed_at!,
          type: transaction.type,
          product_name: transaction.bullion_products?.name || 'Unknown',
          quantity: transaction.quantity,
          price_per_unit: transaction.price_per_unit,
          total_amount: transaction.total_amount,
          fees: transaction.fees || 0
        }

        totalFees += transaction.fees || 0

        if (transaction.type === 'buy') {
          totalPurchases += transaction.total_amount
          
          // Add to holdings for FIFO calculation
          if (!holdings[transaction.product_id]) {
            holdings[transaction.product_id] = []
          }
          holdings[transaction.product_id].push({
            quantity: transaction.quantity,
            price: transaction.price_per_unit,
            date: transaction.completed_at!
          })
        } else if (transaction.type === 'sell') {
          totalSales += transaction.total_amount

          // Calculate gains using FIFO method
          if (holdings[transaction.product_id] && holdings[transaction.product_id].length > 0) {
            let remainingQuantity = transaction.quantity
            let totalCostBasis = 0

            while (remainingQuantity > 0 && holdings[transaction.product_id].length > 0) {
              const oldestHolding = holdings[transaction.product_id][0]
              const quantityToSell = Math.min(remainingQuantity, oldestHolding.quantity)
              
              totalCostBasis += quantityToSell * oldestHolding.price
              
              // Calculate holding period
              const buyDate = new Date(oldestHolding.date)
              const sellDate = new Date(transaction.completed_at!)
              const holdingPeriodDays = Math.floor((sellDate.getTime() - buyDate.getTime()) / (1000 * 60 * 60 * 24))
              
              taxTransaction.holding_period_days = holdingPeriodDays
              
              // Update remaining quantity
              remainingQuantity -= quantityToSell
              oldestHolding.quantity -= quantityToSell
              
              if (oldestHolding.quantity <= 0) {
                holdings[transaction.product_id].shift()
              }
            }

            // Calculate gain/loss
            const saleProceeds = transaction.quantity * transaction.price_per_unit
            const gainLoss = saleProceeds - totalCostBasis - (transaction.fees || 0)
            
            taxTransaction.gain_loss = gainLoss
            
            // Determine if short-term or long-term (3 years for gold/silver in India)
            if (taxTransaction.holding_period_days && taxTransaction.holding_period_days > 1095) {
              taxTransaction.gain_type = 'long_term'
              longTermGains += gainLoss
            } else {
              taxTransaction.gain_type = 'short_term'
              shortTermGains += gainLoss
            }
          }
        }

        taxTransactions.push(taxTransaction)
      }

      // Calculate total tax liability (simplified - actual rates may vary)
      // Short-term gains taxed as per income tax slab
      // Long-term gains: 20% with indexation benefit for gold/silver
      const totalTaxLiability = (shortTermGains * 0.30) + (longTermGains * 0.20) // Assuming 30% for short-term

      const report: TaxReportData = {
        financial_year: financialYear,
        total_purchases: totalPurchases,
        total_sales: totalSales,
        short_term_gains: shortTermGains,
        long_term_gains: longTermGains,
        total_fees: totalFees,
        total_tax_liability: Math.max(0, totalTaxLiability), // No negative tax
        transactions: taxTransactions
      }

      return { report }
    } catch (error) {
      console.error('Error generating tax report:', error)
      return { report: null as any, error: 'Failed to generate tax report' }
    }
  }

  /**
   * Generate profit and loss report
   */
  static async generateProfitLossReport(filters: ReportFilters): Promise<{ report: ProfitLossReport; error?: string }> {
    try {
      const { date_from, date_to } = filters
      
      let query = supabase
        .from('trades')
        .select(`
          *,
          bullion_products (
            name,
            symbol,
            type,
            current_price
          )
        `)
        .eq('status', 'completed')

      if (date_from) query = query.gte('completed_at', date_from)
      if (date_to) query = query.lte('completed_at', date_to)

      const { data: transactions, error } = await query.order('completed_at', { ascending: true })

      if (error) {
        return { report: null as any, error: error.message }
      }

      // Calculate metrics
      let totalInvested = 0
      let totalCurrentValue = 0
      let realizedPnL = 0
      let totalFees = 0
      let winningTrades = 0
      let losingTrades = 0
      
      const holdings: { [productId: string]: { quantity: number; avgPrice: number; currentPrice: number } } = {}
      const assetPerformance: { [productName: string]: number } = {}

      for (const transaction of transactions) {
        totalFees += transaction.fees || 0

        if (transaction.type === 'buy') {
          totalInvested += transaction.total_amount
          
          // Update holdings
          const productId = transaction.product_id
          if (!holdings[productId]) {
            holdings[productId] = {
              quantity: 0,
              avgPrice: 0,
              currentPrice: transaction.bullion_products?.current_price || 0
            }
          }
          
          const currentHolding = holdings[productId]
          const newTotalQuantity = currentHolding.quantity + transaction.quantity
          const newAvgPrice = ((currentHolding.quantity * currentHolding.avgPrice) + 
                              (transaction.quantity * transaction.price_per_unit)) / newTotalQuantity
          
          holdings[productId] = {
            quantity: newTotalQuantity,
            avgPrice: newAvgPrice,
            currentPrice: transaction.bullion_products?.current_price || 0
          }
        } else if (transaction.type === 'sell') {
          // Calculate realized P&L for this sale
          const productId = transaction.product_id
          if (holdings[productId]) {
            const costBasis = transaction.quantity * holdings[productId].avgPrice
            const saleProceeds = transaction.total_amount
            const tradePnL = saleProceeds - costBasis - (transaction.fees || 0)
            
            realizedPnL += tradePnL
            
            if (tradePnL > 0) winningTrades++
            else if (tradePnL < 0) losingTrades++
            
            // Update asset performance
            const productName = transaction.bullion_products?.name || 'Unknown'
            assetPerformance[productName] = (assetPerformance[productName] || 0) + tradePnL
            
            // Update holdings
            holdings[productId].quantity -= transaction.quantity
          }
        }
      }

      // Calculate unrealized P&L from current holdings
      let unrealizedPnL = 0
      for (const holding of Object.values(holdings)) {
        if (holding.quantity > 0) {
          const currentValue = holding.quantity * holding.currentPrice
          const costBasis = holding.quantity * holding.avgPrice
          unrealizedPnL += (currentValue - costBasis)
          totalCurrentValue += currentValue
        }
      }

      const totalPnL = realizedPnL + unrealizedPnL
      const netPnL = totalPnL - totalFees
      const roiPercentage = totalInvested > 0 ? (netPnL / totalInvested) * 100 : 0
      const winRate = (winningTrades + losingTrades) > 0 ? (winningTrades / (winningTrades + losingTrades)) * 100 : 0

      // Find best and worst performing assets
      const sortedAssets = Object.entries(assetPerformance).sort(([,a], [,b]) => b - a)
      const bestPerformingAsset = sortedAssets.length > 0 ? sortedAssets[0][0] : 'N/A'
      const worstPerformingAsset = sortedAssets.length > 0 ? sortedAssets[sortedAssets.length - 1][0] : 'N/A'

      const period = date_from && date_to ? `${date_from} to ${date_to}` : 'All Time'

      const report: ProfitLossReport = {
        period,
        total_invested: totalInvested,
        total_current_value: totalCurrentValue,
        realized_pnl: realizedPnL,
        unrealized_pnl: unrealizedPnL,
        total_pnl: totalPnL,
        total_fees: totalFees,
        net_pnl: netPnL,
        roi_percentage: roiPercentage,
        transactions_count: transactions.length,
        winning_trades: winningTrades,
        losing_trades: losingTrades,
        win_rate: winRate,
        best_performing_asset: bestPerformingAsset,
        worst_performing_asset: worstPerformingAsset
      }

      return { report }
    } catch (error) {
      console.error('Error generating P&L report:', error)
      return { report: null as any, error: 'Failed to generate P&L report' }
    }
  }

  /**
   * Export report in specified format
   */
  static async exportReport(
    reportType: ReportType,
    format: ReportFormat,
    filters: ReportFilters
  ): Promise<{ url: string; error?: string }> {
    try {
      const queryParams = new URLSearchParams({
        type: reportType,
        format,
        ...Object.fromEntries(
          Object.entries(filters).filter(([, value]) => value !== undefined && value !== null)
        )
      })

      const response = await fetch(`/api/reports/export?${queryParams.toString()}`, {
        method: 'GET',
      })

      if (!response.ok) {
        const result = await response.json()
        return { url: '', error: result.error || 'Failed to export report' }
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)

      return { url }
    } catch (error) {
      console.error('Error exporting report:', error)
      return { url: '', error: 'Failed to export report' }
    }
  }
}
