import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/database'

export type Transaction = Database['public']['Tables']['trades']['Row'] & {
  bullion_products?: Database['public']['Tables']['bullion_products']['Row']
}

export type TransactionStatus = Database['public']['Enums']['trade_status']
export type TransactionType = Database['public']['Enums']['trade_type']

export interface TransactionFilters {
  status?: TransactionStatus | 'all'
  type?: TransactionType | 'all'
  product_id?: string
  date_from?: string
  date_to?: string
  limit?: number
  offset?: number
}

export interface TransactionStats {
  total_transactions: number
  total_volume: number
  total_fees: number
  buy_volume: number
  sell_volume: number
  completed_transactions: number
  pending_transactions: number
  cancelled_transactions: number
  profit_loss: number
}

export interface PaymentMethod {
  id: string
  type: 'bank_transfer' | 'upi' | 'card' | 'wallet'
  name: string
  details: Record<string, any>
  is_default: boolean
  is_active: boolean
}

export interface Receipt {
  id: string
  transaction_id: string
  receipt_number: string
  amount: number
  fees: number
  tax: number
  total_amount: number
  payment_method: string
  status: 'generated' | 'sent' | 'downloaded'
  generated_at: string
  pdf_url?: string
}

export class TransactionService {
  /**
   * Get user transactions with filtering and pagination
   */
  static async getTransactions(filters: TransactionFilters = {}): Promise<{
    transactions: Transaction[]
    total: number
    error?: string
  }> {
    try {
      let query = supabase
        .from('trades')
        .select(`
          *,
          bullion_products (
            id,
            name,
            symbol,
            type,
            purity,
            weight,
            weight_unit,
            current_price,
            image_url
          )
        `, { count: 'exact' })
        .order('created_at', { ascending: false })

      // Apply filters
      if (filters.status && filters.status !== 'all') {
        query = query.eq('status', filters.status)
      }

      if (filters.type && filters.type !== 'all') {
        query = query.eq('type', filters.type)
      }

      if (filters.product_id) {
        query = query.eq('product_id', filters.product_id)
      }

      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from)
      }

      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to)
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit)
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
      }

      const { data: transactions, error, count } = await query

      if (error) {
        console.error('Error fetching transactions:', error)
        return { transactions: [], total: 0, error: error.message }
      }

      return {
        transactions: transactions || [],
        total: count || 0
      }
    } catch (error) {
      console.error('Error in getTransactions:', error)
      return { transactions: [], total: 0, error: 'Failed to fetch transactions' }
    }
  }

  /**
   * Get transaction statistics
   */
  static async getTransactionStats(): Promise<{ stats: TransactionStats; error?: string }> {
    try {
      const { data: transactions, error } = await supabase
        .from('trades')
        .select('type, status, total_amount, fees')

      if (error) {
        console.error('Error fetching transaction stats:', error)
        return { stats: null as any, error: error.message }
      }

      const stats: TransactionStats = {
        total_transactions: transactions.length,
        total_volume: 0,
        total_fees: 0,
        buy_volume: 0,
        sell_volume: 0,
        completed_transactions: 0,
        pending_transactions: 0,
        cancelled_transactions: 0,
        profit_loss: 0
      }

      transactions.forEach(transaction => {
        stats.total_volume += Math.abs(transaction.total_amount)
        stats.total_fees += transaction.fees || 0

        if (transaction.type === 'buy') {
          stats.buy_volume += transaction.total_amount
        } else {
          stats.sell_volume += Math.abs(transaction.total_amount)
        }

        switch (transaction.status) {
          case 'completed':
            stats.completed_transactions++
            break
          case 'pending':
            stats.pending_transactions++
            break
          case 'cancelled':
            stats.cancelled_transactions++
            break
        }
      })

      // Calculate profit/loss (simplified - sell volume minus buy volume)
      stats.profit_loss = stats.sell_volume - stats.buy_volume

      return { stats }
    } catch (error) {
      console.error('Error in getTransactionStats:', error)
      return { stats: null as any, error: 'Failed to fetch transaction statistics' }
    }
  }

  /**
   * Get single transaction by ID
   */
  static async getTransaction(id: string): Promise<{ transaction: Transaction; error?: string }> {
    try {
      const { data: transaction, error } = await supabase
        .from('trades')
        .select(`
          *,
          bullion_products (
            id,
            name,
            symbol,
            type,
            purity,
            weight,
            weight_unit,
            current_price,
            image_url
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching transaction:', error)
        return { transaction: null as any, error: error.message }
      }

      return { transaction }
    } catch (error) {
      console.error('Error in getTransaction:', error)
      return { transaction: null as any, error: 'Failed to fetch transaction' }
    }
  }

  /**
   * Generate receipt for a transaction
   */
  static async generateReceipt(transactionId: string): Promise<{ receipt: Receipt; error?: string }> {
    try {
      const response = await fetch(`/api/transactions/${transactionId}/receipt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (!response.ok) {
        return { receipt: null as any, error: result.error || 'Failed to generate receipt' }
      }

      return { receipt: result.receipt }
    } catch (error) {
      console.error('Error generating receipt:', error)
      return { receipt: null as any, error: 'Network error occurred' }
    }
  }

  /**
   * Download receipt PDF
   */
  static async downloadReceipt(receiptId: string): Promise<{ url: string; error?: string }> {
    try {
      const response = await fetch(`/api/receipts/${receiptId}/download`, {
        method: 'GET',
      })

      if (!response.ok) {
        const result = await response.json()
        return { url: '', error: result.error || 'Failed to download receipt' }
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      return { url }
    } catch (error) {
      console.error('Error downloading receipt:', error)
      return { url: '', error: 'Network error occurred' }
    }
  }

  /**
   * Format transaction for display
   */
  static formatTransaction(transaction: Transaction) {
    const formatPrice = (price: number) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price)
    }

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    return {
      ...transaction,
      formatted_amount: formatPrice(transaction.total_amount),
      formatted_price: formatPrice(transaction.price_per_unit),
      formatted_fees: formatPrice(transaction.fees || 0),
      formatted_date: formatDate(transaction.created_at),
      formatted_completed_date: transaction.completed_at ? formatDate(transaction.completed_at) : null,
      status_color: this.getStatusColor(transaction.status),
      type_color: transaction.type === 'buy' ? 'text-green-600' : 'text-red-600'
    }
  }

  /**
   * Get status color for UI
   */
  static getStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  /**
   * Export transactions to CSV
   */
  static async exportTransactions(filters: TransactionFilters = {}): Promise<{ url: string; error?: string }> {
    try {
      const queryParams = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== 'all') {
          queryParams.append(key, value.toString())
        }
      })

      const response = await fetch(`/api/transactions/export?${queryParams.toString()}`, {
        method: 'GET',
      })

      if (!response.ok) {
        const result = await response.json()
        return { url: '', error: result.error || 'Failed to export transactions' }
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      return { url }
    } catch (error) {
      console.error('Error exporting transactions:', error)
      return { url: '', error: 'Network error occurred' }
    }
  }
}
