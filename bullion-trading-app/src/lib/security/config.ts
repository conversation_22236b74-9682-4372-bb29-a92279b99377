// Security configuration for the bullion trading application
export const SecurityConfig = {
  // Authentication settings
  auth: {
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    refreshTokenTimeout: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    passwordMinLength: 8,
    passwordRequireSpecialChars: true,
    passwordRequireNumbers: true,
    passwordRequireUppercase: true,
    passwordRequireLowercase: true,
    otpLength: 6,
    otpExpiry: 5 * 60 * 1000, // 5 minutes
    otpMaxAttempts: 3,
    twoFactorRequired: false, // Can be enabled for high-value accounts
  },

  // Rate limiting settings
  rateLimit: {
    // General API endpoints
    api: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
    },
    // Authentication endpoints
    auth: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 10,
    },
    // Trading endpoints
    trading: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 20,
    },
    // OTP endpoints
    otp: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 3,
    },
    // Report generation
    reports: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 5,
    },
    // Price data
    prices: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60,
    },
    // File uploads
    uploads: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
    },
  },

  // Request validation settings
  validation: {
    maxRequestSize: 10 * 1024 * 1024, // 10MB
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedFileTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/csv',
      'application/json'
    ],
    maxFilenameLength: 255,
    maxStringLength: 1000,
    maxArrayLength: 100,
    enableThreatDetection: true,
    blockSuspiciousRequests: true,
  },

  // Encryption settings
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32, // 256 bits
    ivLength: 16, // 128 bits
    tagLength: 16, // 128 bits
    saltLength: 32, // 256 bits
    pbkdf2Iterations: 100000,
    bcryptRounds: 12,
  },

  // Audit logging settings
  audit: {
    enableLogging: true,
    logLevel: 'INFO', // DEBUG, INFO, WARN, ERROR, CRITICAL
    retentionDays: 90,
    batchSize: 100,
    flushInterval: 5000, // 5 seconds
    logSensitiveData: false,
    enableRealTimeAlerts: true,
    criticalEventNotification: true,
  },

  // Security headers settings
  headers: {
    enableHSTS: true,
    hstsMaxAge: 31536000, // 1 year
    hstsIncludeSubdomains: true,
    hstsPreload: true,
    enableCSP: true,
    cspReportOnly: false,
    enableXFrameOptions: true,
    xFrameOptions: 'DENY',
    enableXContentTypeOptions: true,
    enableReferrerPolicy: true,
    referrerPolicy: 'strict-origin-when-cross-origin',
    enablePermissionsPolicy: true,
    enableCORP: true,
    enableCOEP: false, // May break some functionality
    enableCOOP: true,
  },

  // CORS settings
  cors: {
    enabled: true,
    allowedOrigins: process.env.NODE_ENV === 'production' 
      ? [process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com']
      : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
      'Cache-Control',
      'X-File-Name',
      'X-CSRF-Token'
    ],
    exposedHeaders: [
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset',
      'X-Total-Count',
      'X-Request-ID'
    ],
    credentials: true,
    maxAge: 86400, // 24 hours
  },

  // Session security settings
  session: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict' as const,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    rolling: true, // Extend session on activity
    regenerateOnLogin: true,
    cookieName: 'bullion-session',
    csrfTokenName: 'csrf-token',
  },

  // IP blocking and geolocation settings
  ipSecurity: {
    enableBlocking: true,
    maxFailedAttempts: 10,
    blockDuration: 60 * 60 * 1000, // 1 hour
    enableGeolocation: false, // Requires external service
    allowedCountries: [], // Empty = allow all
    blockedCountries: [], // Countries to block
    enableVPNDetection: false, // Requires external service
    blockVPN: false,
    enableTorDetection: false, // Requires external service
    blockTor: false,
  },

  // Financial transaction security
  financial: {
    maxTransactionAmount: 1000000, // ₹10,00,000
    dailyTransactionLimit: 5000000, // ₹50,00,000
    monthlyTransactionLimit: ********, // ₹5,00,00,000
    requireAdditionalAuthForLargeTransactions: true,
    largeTransactionThreshold: 100000, // ₹1,00,000
    enableTransactionMonitoring: true,
    suspiciousActivityThreshold: 10, // Number of transactions
    enableAMLChecks: true,
    enableFraudDetection: true,
    freezeAccountOnSuspiciousActivity: false, // Manual review instead
  },

  // API security settings
  api: {
    enableVersioning: true,
    currentVersion: 'v1',
    deprecationWarnings: true,
    enableDocumentation: process.env.NODE_ENV !== 'production',
    enableMetrics: true,
    enableHealthChecks: true,
    timeoutMs: 30000, // 30 seconds
    maxConcurrentRequests: 100,
    enableRequestLogging: true,
    enableResponseCompression: true,
  },

  // Data protection settings
  dataProtection: {
    enableEncryptionAtRest: true,
    enableEncryptionInTransit: true,
    enableDataMasking: true,
    enableDataAnonymization: false, // For analytics
    dataRetentionDays: 2555, // 7 years for financial records
    enableRightToErasure: true, // GDPR compliance
    enableDataPortability: true, // GDPR compliance
    enableConsentManagement: true,
    enablePrivacyControls: true,
  },

  // Monitoring and alerting settings
  monitoring: {
    enableRealTimeMonitoring: true,
    enablePerformanceMonitoring: true,
    enableSecurityMonitoring: true,
    enableBusinessMetrics: true,
    alertThresholds: {
      errorRate: 0.05, // 5%
      responseTime: 2000, // 2 seconds
      failedLogins: 10, // per minute
      suspiciousActivity: 5, // per minute
      systemLoad: 0.8, // 80%
      memoryUsage: 0.85, // 85%
    },
    notificationChannels: {
      email: true,
      sms: false,
      webhook: false,
      slack: false,
    },
  },

  // Compliance settings
  compliance: {
    enableGDPR: true,
    enableCCPA: false,
    enableSOX: true, // For financial compliance
    enablePCI: false, // If handling card data
    enableISO27001: true,
    enableAuditTrails: true,
    enableDataClassification: true,
    enableRiskAssessment: true,
    complianceReportingInterval: 'monthly',
  },

  // Development and testing settings
  development: {
    enableDebugMode: process.env.NODE_ENV === 'development',
    enableTestingEndpoints: process.env.NODE_ENV !== 'production',
    enableMockData: process.env.NODE_ENV === 'development',
    enablePerformanceProfiling: process.env.NODE_ENV === 'development',
    logLevel: process.env.NODE_ENV === 'development' ? 'DEBUG' : 'INFO',
    enableHotReload: process.env.NODE_ENV === 'development',
  }
}

// Environment-specific overrides
export const getSecurityConfig = () => {
  const config = { ...SecurityConfig }
  
  // Production overrides
  if (process.env.NODE_ENV === 'production') {
    config.validation.enableThreatDetection = true
    config.validation.blockSuspiciousRequests = true
    config.audit.enableLogging = true
    config.audit.enableRealTimeAlerts = true
    config.headers.enableHSTS = true
    config.headers.cspReportOnly = false
    config.ipSecurity.enableBlocking = true
    config.financial.enableTransactionMonitoring = true
    config.financial.enableFraudDetection = true
    config.monitoring.enableRealTimeMonitoring = true
    config.monitoring.enableSecurityMonitoring = true
  }
  
  // Development overrides
  if (process.env.NODE_ENV === 'development') {
    config.rateLimit.api.maxRequests = 1000 // More lenient for development
    config.headers.cspReportOnly = true // Don't block in development
    config.ipSecurity.enableBlocking = false // Don't block IPs in development
    config.audit.logLevel = 'DEBUG'
  }
  
  return config
}

// Validate required environment variables
export const validateSecurityEnvironment = () => {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'ENCRYPTION_KEY'
  ]
  
  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
  
  // Validate encryption key length
  const encryptionKey = process.env.ENCRYPTION_KEY
  if (encryptionKey && encryptionKey.length < 32) {
    console.warn('ENCRYPTION_KEY should be at least 32 characters long for optimal security')
  }
  
  return true
}

export default SecurityConfig
