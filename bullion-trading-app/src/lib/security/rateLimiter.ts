import { NextRequest } from 'next/server'

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  keyGenerator?: (req: NextRequest) => string
}

interface RateLimitEntry {
  count: number
  resetTime: number
}

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, RateLimitEntry>()

// Cleanup expired entries every 5 minutes
setInterval(() => {
  const now = Date.now()
  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime < now) {
      rateLimitStore.delete(key)
    }
  }
}, 5 * 60 * 1000)

export class RateLimiter {
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = config
  }

  private getKey(req: NextRequest): string {
    if (this.config.keyGenerator) {
      return this.config.keyGenerator(req)
    }

    // Default key generation: IP + User-Agent + Path
    const ip = this.getClientIP(req)
    const userAgent = req.headers.get('user-agent') || 'unknown'
    const path = req.nextUrl.pathname
    
    return `${ip}:${userAgent.slice(0, 50)}:${path}`
  }

  private getClientIP(req: NextRequest): string {
    // Check various headers for the real IP
    const forwarded = req.headers.get('x-forwarded-for')
    const realIP = req.headers.get('x-real-ip')
    const cfConnectingIP = req.headers.get('cf-connecting-ip')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    if (realIP) {
      return realIP
    }
    
    if (cfConnectingIP) {
      return cfConnectingIP
    }
    
    return req.ip || 'unknown'
  }

  public check(req: NextRequest): {
    allowed: boolean
    remaining: number
    resetTime: number
    totalHits: number
  } {
    const key = this.getKey(req)
    const now = Date.now()
    const windowStart = now - this.config.windowMs

    let entry = rateLimitStore.get(key)

    if (!entry || entry.resetTime < now) {
      // Create new entry or reset expired entry
      entry = {
        count: 0,
        resetTime: now + this.config.windowMs
      }
    }

    entry.count++
    rateLimitStore.set(key, entry)

    const allowed = entry.count <= this.config.maxRequests
    const remaining = Math.max(0, this.config.maxRequests - entry.count)

    return {
      allowed,
      remaining,
      resetTime: entry.resetTime,
      totalHits: entry.count
    }
  }

  public reset(req: NextRequest): void {
    const key = this.getKey(req)
    rateLimitStore.delete(key)
  }
}

// Predefined rate limiters for different endpoints
export const rateLimiters = {
  // General API rate limiter
  api: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
  }),

  // Authentication endpoints (more restrictive)
  auth: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 attempts per 15 minutes
  }),

  // Trading endpoints (moderate)
  trading: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20, // 20 trades per minute
  }),

  // OTP endpoints (very restrictive)
  otp: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 3, // 3 OTP requests per minute
  }),

  // Report generation (restrictive)
  reports: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 reports per minute
  }),

  // Price data (moderate)
  prices: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
  }),

  // File uploads (restrictive)
  uploads: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  })
}

// Rate limit middleware factory
export function createRateLimitMiddleware(limiter: RateLimiter) {
  return (req: NextRequest) => {
    const result = limiter.check(req)
    
    return {
      ...result,
      headers: {
        'X-RateLimit-Limit': limiter['config'].maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
        'X-RateLimit-Window': Math.ceil(limiter['config'].windowMs / 1000).toString(),
      }
    }
  }
}

// Helper function to get rate limit info for specific endpoint
export function getRateLimitInfo(endpoint: keyof typeof rateLimiters, req: NextRequest) {
  const limiter = rateLimiters[endpoint]
  return createRateLimitMiddleware(limiter)(req)
}

// Advanced rate limiting with different strategies
export class AdvancedRateLimiter {
  private limiters: Map<string, RateLimiter> = new Map()

  // Sliding window rate limiter
  createSlidingWindow(key: string, config: RateLimitConfig): RateLimiter {
    const limiter = new RateLimiter(config)
    this.limiters.set(key, limiter)
    return limiter
  }

  // Token bucket rate limiter (for burst handling)
  createTokenBucket(key: string, capacity: number, refillRate: number) {
    // Implementation would go here for token bucket algorithm
    // This is a simplified version
    return new RateLimiter({
      windowMs: 60 * 1000,
      maxRequests: capacity
    })
  }

  // Adaptive rate limiter (adjusts based on system load)
  createAdaptive(key: string, baseConfig: RateLimitConfig) {
    // Implementation would adjust limits based on system metrics
    return new RateLimiter(baseConfig)
  }

  getLimiter(key: string): RateLimiter | undefined {
    return this.limiters.get(key)
  }
}

export const advancedRateLimiter = new AdvancedRateLimiter()

// Rate limiting by user ID (for authenticated requests)
export function createUserRateLimiter(userId: string, config: RateLimitConfig): RateLimiter {
  return new RateLimiter({
    ...config,
    keyGenerator: () => `user:${userId}`
  })
}

// Rate limiting by API key
export function createAPIKeyRateLimiter(apiKey: string, config: RateLimitConfig): RateLimiter {
  return new RateLimiter({
    ...config,
    keyGenerator: () => `apikey:${apiKey}`
  })
}
