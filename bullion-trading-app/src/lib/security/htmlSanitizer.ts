/**
 * Server-safe HTML sanitization utilities
 * This module provides HTML sanitization that works in both server and client environments
 * without relying on browser APIs like window or document.
 */

interface SanitizeOptions {
  allowedTags?: string[]
  allowedAttributes?: { [tag: string]: string[] }
  removeEmptyTags?: boolean
  maxLength?: number
}

const DEFAULT_ALLOWED_TAGS = ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li']
const DEFAULT_ALLOWED_ATTRIBUTES: { [tag: string]: string[] } = {
  'a': ['href', 'title'],
  'img': ['src', 'alt', 'width', 'height']
}

export class ServerSafeHTMLSanitizer {
  /**
   * Sanitize HTML content using server-safe methods
   */
  static sanitize(input: string, options: SanitizeOptions = {}): string {
    if (typeof input !== 'string') return ''
    
    const {
      allowedTags = DEFAULT_ALLOWED_TAGS,
      allowedAttributes = DEFAULT_ALLOWED_ATTRIBUTES,
      removeEmptyTags = true,
      maxLength = 10000
    } = options

    let sanitized = input.substring(0, maxLength)

    // Remove script and style tags completely (including content)
    sanitized = sanitized.replace(/<(script|style)[^>]*>[\s\S]*?<\/\1>/gi, '')
    
    // Remove dangerous event handlers and javascript: URLs
    sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
    sanitized = sanitized.replace(/javascript:/gi, '')
    
    // Process HTML tags
    sanitized = sanitized.replace(/<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>/gi, (match, tagName) => {
      const tag = tagName.toLowerCase()
      
      if (!allowedTags.includes(tag)) {
        return '' // Remove disallowed tags
      }

      // For self-closing tags
      if (match.endsWith('/>')) {
        return `<${tag} />`
      }

      // For opening tags, process attributes if allowed
      if (!match.startsWith('</')) {
        const allowedAttrs = allowedAttributes[tag] || []
        if (allowedAttrs.length === 0) {
          return `<${tag}>`
        }

        // Extract and filter attributes
        const attrRegex = /(\w+)\s*=\s*["']([^"']*)["']/g
        let attrMatch
        const validAttrs: string[] = []

        while ((attrMatch = attrRegex.exec(match)) !== null) {
          const [, attrName, attrValue] = attrMatch
          if (allowedAttrs.includes(attrName.toLowerCase())) {
            // Additional validation for specific attributes
            if (attrName.toLowerCase() === 'href' && !this.isValidURL(attrValue)) {
              continue
            }
            if (attrName.toLowerCase() === 'src' && !this.isValidURL(attrValue)) {
              continue
            }
            validAttrs.push(`${attrName}="${this.escapeAttribute(attrValue)}"`)
          }
        }

        return validAttrs.length > 0 ? `<${tag} ${validAttrs.join(' ')}>` : `<${tag}>`
      }

      // For closing tags
      return `</${tag}>`
    })

    // Remove empty tags if requested
    if (removeEmptyTags) {
      sanitized = sanitized.replace(/<(\w+)>\s*<\/\1>/g, '')
    }

    // Decode HTML entities to prevent double encoding
    sanitized = this.decodeHTMLEntities(sanitized)

    return sanitized.trim()
  }

  /**
   * Strip all HTML tags from input
   */
  static stripHTML(input: string): string {
    if (typeof input !== 'string') return ''
    return input.replace(/<[^>]*>/g, '').trim()
  }

  /**
   * Escape HTML special characters
   */
  static escapeHTML(input: string): string {
    if (typeof input !== 'string') return ''
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  /**
   * Validate URL for href and src attributes
   */
  private static isValidURL(url: string): boolean {
    try {
      const parsed = new URL(url)
      return ['http:', 'https:', 'mailto:'].includes(parsed.protocol)
    } catch {
      // For relative URLs, check if they don't contain dangerous patterns
      return !/^(javascript:|data:|vbscript:)/i.test(url) && 
             !/[<>'"&]/.test(url)
    }
  }

  /**
   * Escape attribute values
   */
  private static escapeAttribute(value: string): string {
    return value
      .replace(/&/g, '&amp;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
  }

  /**
   * Decode common HTML entities
   */
  private static decodeHTMLEntities(input: string): string {
    const entities: { [key: string]: string } = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#x27;': "'",
      '&#x2F;': '/',
      '&nbsp;': ' '
    }

    return input.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity
    })
  }
}

/**
 * Quick sanitization for basic use cases
 */
export function sanitizeHTML(input: string, allowedTags?: string[]): string {
  return ServerSafeHTMLSanitizer.sanitize(input, { allowedTags })
}

/**
 * Strip all HTML tags
 */
export function stripHTML(input: string): string {
  return ServerSafeHTMLSanitizer.stripHTML(input)
}

/**
 * Escape HTML for safe display
 */
export function escapeHTML(input: string): string {
  return ServerSafeHTMLSanitizer.escapeHTML(input)
}
