import { z } from 'zod'
import DOMPurify from 'isomorphic-dompurify'

// Common validation patterns
export const ValidationPatterns = {
  // Email validation
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // Phone number validation (international format)
  phone: /^\+?[1-9]\d{1,14}$/,
  
  // Strong password (at least 8 chars, 1 upper, 1 lower, 1 number, 1 special)
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  
  // UUID validation
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  
  // Alphanumeric with spaces and common punctuation
  safeText: /^[a-zA-Z0-9\s\-_.,!?()]+$/,
  
  // Numbers only
  numbersOnly: /^\d+$/,
  
  // Decimal numbers
  decimal: /^\d+(\.\d{1,8})?$/,
  
  // Currency amount (up to 2 decimal places)
  currency: /^\d+(\.\d{1,2})?$/,
  
  // SQL injection patterns (for detection)
  sqlInjection: /(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror|onclick)/i,
  
  // XSS patterns (for detection)
  xss: /(<script|<iframe|<object|<embed|<link|<meta|javascript:|vbscript:|data:text\/html|on\w+\s*=)/i
}

// Input sanitization functions
export class InputSanitizer {
  // Sanitize HTML content
  static sanitizeHTML(input: string): string {
    if (typeof input !== 'string') return ''
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: []
    })
  }

  // Remove all HTML tags
  static stripHTML(input: string): string {
    if (typeof input !== 'string') return ''
    return input.replace(/<[^>]*>/g, '')
  }

  // Sanitize for SQL (escape single quotes)
  static sanitizeSQL(input: string): string {
    if (typeof input !== 'string') return ''
    return input.replace(/'/g, "''")
  }

  // Remove potentially dangerous characters
  static sanitizeText(input: string): string {
    if (typeof input !== 'string') return ''
    return input
      .replace(/[<>'"&]/g, '') // Remove HTML/XML chars
      .replace(/[{}[\]]/g, '') // Remove JSON chars
      .replace(/[;|&$`]/g, '') // Remove shell chars
      .trim()
  }

  // Sanitize filename
  static sanitizeFilename(filename: string): string {
    if (typeof filename !== 'string') return ''
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace unsafe chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .substring(0, 255) // Limit length
  }

  // Sanitize URL
  static sanitizeURL(url: string): string {
    if (typeof url !== 'string') return ''
    try {
      const parsed = new URL(url)
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        return ''
      }
      return parsed.toString()
    } catch {
      return ''
    }
  }

  // Normalize whitespace
  static normalizeWhitespace(input: string): string {
    if (typeof input !== 'string') return ''
    return input
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim()
  }

  // Remove null bytes and control characters
  static removeControlChars(input: string): string {
    if (typeof input !== 'string') return ''
    return input.replace(/[\x00-\x1F\x7F]/g, '')
  }
}

// Security validation schemas
export const SecuritySchemas = {
  // Safe string (no HTML, limited length)
  safeString: z.string()
    .min(1, 'Field is required')
    .max(1000, 'Field is too long')
    .refine(val => !ValidationPatterns.xss.test(val), 'Invalid characters detected')
    .refine(val => !ValidationPatterns.sqlInjection.test(val), 'Invalid characters detected')
    .transform(val => InputSanitizer.sanitizeText(val)),

  // Email validation
  email: z.string()
    .email('Invalid email format')
    .max(254, 'Email is too long')
    .transform(val => val.toLowerCase().trim()),

  // Phone number validation
  phone: z.string()
    .regex(ValidationPatterns.phone, 'Invalid phone number format')
    .transform(val => val.replace(/\s+/g, '')),

  // Strong password validation
  strongPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(ValidationPatterns.strongPassword, 
      'Password must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character'),

  // UUID validation
  uuid: z.string()
    .regex(ValidationPatterns.uuid, 'Invalid UUID format'),

  // Currency amount validation
  currency: z.string()
    .regex(ValidationPatterns.currency, 'Invalid currency format')
    .transform(val => parseFloat(val))
    .refine(val => val >= 0, 'Amount must be positive')
    .refine(val => val <= 999999999.99, 'Amount is too large'),

  // Quantity validation
  quantity: z.number()
    .positive('Quantity must be positive')
    .max(999999, 'Quantity is too large')
    .refine(val => Number.isFinite(val), 'Invalid quantity'),

  // File upload validation
  filename: z.string()
    .min(1, 'Filename is required')
    .max(255, 'Filename is too long')
    .transform(val => InputSanitizer.sanitizeFilename(val)),

  // URL validation
  url: z.string()
    .url('Invalid URL format')
    .transform(val => InputSanitizer.sanitizeURL(val))
    .refine(val => val !== '', 'Invalid URL'),

  // Date validation
  dateString: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)')
    .refine(val => !isNaN(Date.parse(val)), 'Invalid date'),

  // Pagination parameters
  pagination: z.object({
    page: z.number().int().min(1).max(1000).default(1),
    limit: z.number().int().min(1).max(100).default(20)
  }),

  // Search query validation
  searchQuery: z.string()
    .max(100, 'Search query is too long')
    .transform(val => InputSanitizer.sanitizeText(val))
    .refine(val => val.length >= 2, 'Search query must be at least 2 characters')
}

// Request validation middleware
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return async (data: unknown): Promise<{ success: true; data: T } | { success: false; error: string }> => {
    try {
      const validatedData = await schema.parseAsync(data)
      return { success: true, data: validatedData }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessage = error.errors
          .map(err => `${err.path.join('.')}: ${err.message}`)
          .join(', ')
        return { success: false, error: errorMessage }
      }
      return { success: false, error: 'Validation failed' }
    }
  }
}

// Detect potential security threats in input
export class ThreatDetector {
  static detectSQLInjection(input: string): boolean {
    return ValidationPatterns.sqlInjection.test(input)
  }

  static detectXSS(input: string): boolean {
    return ValidationPatterns.xss.test(input)
  }

  static detectPathTraversal(input: string): boolean {
    return /\.\.[\/\\]/.test(input)
  }

  static detectCommandInjection(input: string): boolean {
    return /[;&|`$(){}[\]<>]/.test(input)
  }

  static analyzeInput(input: string): {
    safe: boolean
    threats: string[]
  } {
    const threats: string[] = []

    if (this.detectSQLInjection(input)) {
      threats.push('SQL Injection')
    }

    if (this.detectXSS(input)) {
      threats.push('XSS')
    }

    if (this.detectPathTraversal(input)) {
      threats.push('Path Traversal')
    }

    if (this.detectCommandInjection(input)) {
      threats.push('Command Injection')
    }

    return {
      safe: threats.length === 0,
      threats
    }
  }
}

// File upload security validation
export class FileValidator {
  private static readonly ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/csv',
    'application/json'
  ]

  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

  static validateFile(file: File): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      errors.push('File size exceeds 10MB limit')
    }

    // Check MIME type
    if (!this.ALLOWED_MIME_TYPES.includes(file.type)) {
      errors.push('File type not allowed')
    }

    // Check filename
    const sanitizedName = InputSanitizer.sanitizeFilename(file.name)
    if (sanitizedName !== file.name) {
      errors.push('Filename contains invalid characters')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  static sanitizeFileContent(content: string, mimeType: string): string {
    switch (mimeType) {
      case 'text/csv':
        return content.replace(/[<>'"&]/g, '') // Remove HTML chars from CSV
      case 'application/json':
        try {
          // Validate and re-stringify JSON to remove any malicious content
          const parsed = JSON.parse(content)
          return JSON.stringify(parsed)
        } catch {
          return ''
        }
      default:
        return content
    }
  }
}
