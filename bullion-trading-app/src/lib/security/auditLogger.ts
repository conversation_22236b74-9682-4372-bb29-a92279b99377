import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Audit event types
export enum AuditEventType {
  // Authentication events
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST',
  PASSWORD_RESET_SUCCESS = 'PASSWORD_RESET_SUCCESS',
  OTP_SENT = 'OTP_SENT',
  OTP_VERIFIED = 'OTP_VERIFIED',
  OTP_FAILED = 'OTP_FAILED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',

  // Trading events
  ORDER_CREATED = 'ORDER_CREATED',
  ORDER_UPDATED = 'ORDER_UPDATED',
  ORDER_CANCELLED = 'ORDER_CANCELLED',
  ORDER_EXECUTED = 'ORDER_EXECUTED',
  TRADE_COMPLETED = 'TRADE_COMPLETED',

  // Financial events
  PAYMENT_INITIATED = 'PAYMENT_INITIATED',
  PAYMENT_SUCCESS = 'PAYMENT_SUCCESS',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  REFUND_INITIATED = 'REFUND_INITIATED',
  REFUND_COMPLETED = 'REFUND_COMPLETED',

  // Data access events
  PROFILE_VIEWED = 'PROFILE_VIEWED',
  PROFILE_UPDATED = 'PROFILE_UPDATED',
  PORTFOLIO_VIEWED = 'PORTFOLIO_VIEWED',
  TRANSACTION_HISTORY_VIEWED = 'TRANSACTION_HISTORY_VIEWED',
  REPORT_GENERATED = 'REPORT_GENERATED',
  DATA_EXPORTED = 'DATA_EXPORTED',

  // Security events
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  IP_BLOCKED = 'IP_BLOCKED',
  MALICIOUS_REQUEST = 'MALICIOUS_REQUEST',

  // System events
  API_ERROR = 'API_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',

  // Admin events
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  ADMIN_ACTION = 'ADMIN_ACTION',
  CONFIGURATION_CHANGED = 'CONFIGURATION_CHANGED'
}

// Risk levels for audit events
export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Audit log entry interface
export interface AuditLogEntry {
  id?: string
  user_id?: string
  session_id?: string
  event_type: AuditEventType
  event_description: string
  risk_level: RiskLevel
  ip_address?: string
  user_agent?: string
  request_path?: string
  request_method?: string
  request_headers?: Record<string, string>
  request_body?: Record<string, any>
  response_status?: number
  response_time?: number
  metadata?: Record<string, any>
  created_at?: string
}

// Audit logger class
export class AuditLogger {
  private static instance: AuditLogger
  private logQueue: AuditLogEntry[] = []
  private isProcessing = false

  private constructor() {
    // Process log queue every 5 seconds
    setInterval(() => {
      this.processLogQueue()
    }, 5000)
  }

  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger()
    }
    return AuditLogger.instance
  }

  // Log an audit event
  public async log(entry: Omit<AuditLogEntry, 'id' | 'created_at'>): Promise<void> {
    const logEntry: AuditLogEntry = {
      ...entry,
      created_at: new Date().toISOString()
    }

    // Add to queue for batch processing
    this.logQueue.push(logEntry)

    // For critical events, log immediately
    if (entry.risk_level === RiskLevel.CRITICAL) {
      await this.logImmediately(logEntry)
    }
  }

  // Log immediately (for critical events)
  private async logImmediately(entry: AuditLogEntry): Promise<void> {
    try {
      const { error } = await supabase
        .from('audit_logs')
        .insert([entry])

      if (error) {
        console.error('Failed to log audit event:', error)
        // In production, you might want to send this to an external logging service
      }
    } catch (error) {
      console.error('Audit logging error:', error)
    }
  }

  // Process the log queue in batches
  private async processLogQueue(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      const batch = this.logQueue.splice(0, 100) // Process up to 100 entries at once

      const { error } = await supabase
        .from('audit_logs')
        .insert(batch)

      if (error) {
        console.error('Failed to process audit log batch:', error)
        // Re-add failed entries to the queue
        this.logQueue.unshift(...batch)
      }
    } catch (error) {
      console.error('Audit log batch processing error:', error)
    } finally {
      this.isProcessing = false
    }
  }

  // Helper methods for common audit events
  public async logAuthentication(
    eventType: AuditEventType,
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
    success: boolean = true,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      user_id: userId,
      event_type: eventType,
      event_description: `Authentication event: ${eventType}`,
      risk_level: success ? RiskLevel.LOW : RiskLevel.MEDIUM,
      ip_address: ipAddress,
      user_agent: userAgent,
      metadata
    })
  }

  public async logTradingActivity(
    eventType: AuditEventType,
    userId: string,
    orderId?: string,
    amount?: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      user_id: userId,
      event_type: eventType,
      event_description: `Trading activity: ${eventType}`,
      risk_level: RiskLevel.MEDIUM,
      metadata: {
        order_id: orderId,
        amount,
        ...metadata
      }
    })
  }

  public async logSecurityEvent(
    eventType: AuditEventType,
    description: string,
    ipAddress?: string,
    userId?: string,
    riskLevel: RiskLevel = RiskLevel.HIGH,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      user_id: userId,
      event_type: eventType,
      event_description: description,
      risk_level: riskLevel,
      ip_address: ipAddress,
      metadata
    })
  }

  public async logDataAccess(
    eventType: AuditEventType,
    userId: string,
    resourceType: string,
    resourceId?: string,
    ipAddress?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      user_id: userId,
      event_type: eventType,
      event_description: `Data access: ${resourceType}`,
      risk_level: RiskLevel.LOW,
      ip_address: ipAddress,
      metadata: {
        resource_type: resourceType,
        resource_id: resourceId,
        ...metadata
      }
    })
  }

  public async logSystemError(
    error: Error,
    context: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      user_id: userId,
      event_type: AuditEventType.SYSTEM_ERROR,
      event_description: `System error in ${context}: ${error.message}`,
      risk_level: RiskLevel.MEDIUM,
      metadata: {
        error_name: error.name,
        error_message: error.message,
        error_stack: error.stack,
        context,
        ...metadata
      }
    })
  }

  // Query audit logs with filters
  public async queryLogs(filters: {
    userId?: string
    eventType?: AuditEventType
    riskLevel?: RiskLevel
    startDate?: string
    endDate?: string
    ipAddress?: string
    limit?: number
    offset?: number
  }): Promise<{ data: AuditLogEntry[] | null; error: any }> {
    let query = supabase
      .from('audit_logs')
      .select('*')
      .order('created_at', { ascending: false })

    if (filters.userId) {
      query = query.eq('user_id', filters.userId)
    }

    if (filters.eventType) {
      query = query.eq('event_type', filters.eventType)
    }

    if (filters.riskLevel) {
      query = query.eq('risk_level', filters.riskLevel)
    }

    if (filters.startDate) {
      query = query.gte('created_at', filters.startDate)
    }

    if (filters.endDate) {
      query = query.lte('created_at', filters.endDate)
    }

    if (filters.ipAddress) {
      query = query.eq('ip_address', filters.ipAddress)
    }

    if (filters.limit) {
      query = query.limit(filters.limit)
    }

    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1)
    }

    return await query
  }

  // Get audit statistics
  public async getAuditStats(timeframe: 'day' | 'week' | 'month' = 'day'): Promise<{
    totalEvents: number
    eventsByType: Record<string, number>
    eventsByRisk: Record<string, number>
    topIPs: Array<{ ip: string; count: number }>
  }> {
    const now = new Date()
    let startDate: Date

    switch (timeframe) {
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
    }

    const { data: logs } = await supabase
      .from('audit_logs')
      .select('event_type, risk_level, ip_address')
      .gte('created_at', startDate.toISOString())

    if (!logs) {
      return {
        totalEvents: 0,
        eventsByType: {},
        eventsByRisk: {},
        topIPs: []
      }
    }

    const eventsByType: Record<string, number> = {}
    const eventsByRisk: Record<string, number> = {}
    const ipCounts: Record<string, number> = {}

    logs.forEach(log => {
      eventsByType[log.event_type] = (eventsByType[log.event_type] || 0) + 1
      eventsByRisk[log.risk_level] = (eventsByRisk[log.risk_level] || 0) + 1
      
      if (log.ip_address) {
        ipCounts[log.ip_address] = (ipCounts[log.ip_address] || 0) + 1
      }
    })

    const topIPs = Object.entries(ipCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([ip, count]) => ({ ip, count }))

    return {
      totalEvents: logs.length,
      eventsByType,
      eventsByRisk,
      topIPs
    }
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance()

// Convenience functions
export const logAuth = auditLogger.logAuthentication.bind(auditLogger)
export const logTrade = auditLogger.logTradingActivity.bind(auditLogger)
export const logSecurity = auditLogger.logSecurityEvent.bind(auditLogger)
export const logDataAccess = auditLogger.logDataAccess.bind(auditLogger)
export const logError = auditLogger.logSystemError.bind(auditLogger)
