import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/database'
import { validateRe<PERSON>, SecuritySchemas, ThreatDetector } from './validation'
import { auditLogger, AuditEventType, RiskLevel } from './auditLogger'
import { applySecurityHeaders, applyCORSHeaders, applyAPISecurityHeaders } from './headers'
import { z } from 'zod'

// API middleware configuration
interface APIMiddlewareConfig {
  requireAuth?: boolean
  requireVerification?: boolean
  validateInput?: z.ZodSchema<any>
  rateLimit?: {
    windowMs: number
    maxRequests: number
  }
  auditLog?: boolean
  allowedMethods?: string[]
  requireCSRF?: boolean
}

// Request context interface
export interface RequestContext {
  user?: {
    id: string
    email: string
    is_verified: boolean
  }
  clientIP: string
  userAgent: string
  startTime: number
  requestId: string
}

// Enhanced API route wrapper with security features
export function withSecurity(
  handler: (req: NextRequest, context: RequestContext) => Promise<NextResponse>,
  config: APIMiddlewareConfig = {}
) {
  return async (req: NextRequest, routeParams?: any) => {
    const startTime = Date.now()
    const requestId = crypto.randomUUID()
    const clientIP = getClientIP(req)
    const userAgent = req.headers.get('user-agent') || 'unknown'
    const origin = req.headers.get('origin')

    try {
      // Method validation
      if (config.allowedMethods && !config.allowedMethods.includes(req.method)) {
        return createErrorResponse(
          'Method not allowed',
          405,
          origin,
          { allowed_methods: config.allowedMethods }
        )
      }

      // CSRF protection for state-changing operations
      if (config.requireCSRF && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
        const csrfToken = req.headers.get('x-csrf-token')
        const sessionCSRF = req.cookies.get('csrf-token')?.value
        
        if (!csrfToken || !sessionCSRF || csrfToken !== sessionCSRF) {
          await auditLogger.logSecurityEvent(
            AuditEventType.SECURITY_VIOLATION,
            'CSRF token validation failed',
            clientIP,
            undefined,
            RiskLevel.HIGH,
            { path: req.nextUrl.pathname, method: req.method }
          )
          
          return createErrorResponse('CSRF token validation failed', 403, origin)
        }
      }

      // Input threat detection
      const url = req.nextUrl.pathname + req.nextUrl.search
      const threatAnalysis = ThreatDetector.analyzeInput(url)
      
      if (!threatAnalysis.safe) {
        await auditLogger.logSecurityEvent(
          AuditEventType.MALICIOUS_REQUEST,
          `Malicious input detected: ${threatAnalysis.threats.join(', ')}`,
          clientIP,
          undefined,
          RiskLevel.CRITICAL,
          { 
            threats: threatAnalysis.threats,
            url,
            user_agent: userAgent
          }
        )
        
        return createErrorResponse('Request blocked for security reasons', 403, origin)
      }

      // Authentication
      let user: RequestContext['user'] | undefined
      
      if (config.requireAuth) {
        const supabase = createRouteHandlerClient<Database>({ cookies })
        const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
        
        if (authError || !authUser) {
          await auditLogger.logSecurityEvent(
            AuditEventType.UNAUTHORIZED_ACCESS,
            `Unauthorized API access to ${req.nextUrl.pathname}`,
            clientIP,
            undefined,
            RiskLevel.MEDIUM,
            { path: req.nextUrl.pathname, method: req.method }
          )
          
          return createErrorResponse('Unauthorized', 401, origin)
        }

        // Check if user verification is required
        if (config.requireVerification) {
          const { data: userProfile } = await supabase
            .from('users')
            .select('is_verified')
            .eq('id', authUser.id)
            .single()

          if (!userProfile?.is_verified) {
            return createErrorResponse('Account verification required', 403, origin)
          }
        }

        user = {
          id: authUser.id,
          email: authUser.email || '',
          is_verified: true // We've already checked this above if required
        }
      }

      // Input validation
      if (config.validateInput) {
        let requestData: any = {}
        
        if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
          try {
            requestData = await req.json()
          } catch (error) {
            return createErrorResponse('Invalid JSON in request body', 400, origin)
          }
        }

        // Add query parameters to validation data
        const searchParams = Object.fromEntries(req.nextUrl.searchParams.entries())
        requestData = { ...requestData, ...searchParams }

        const validation = await validateRequest(config.validateInput)(requestData)
        
        if (!validation.success) {
          await auditLogger.logSecurityEvent(
            AuditEventType.SECURITY_VIOLATION,
            `Input validation failed: ${validation.error}`,
            clientIP,
            user?.id,
            RiskLevel.LOW,
            { 
              path: req.nextUrl.pathname,
              validation_error: validation.error
            }
          )
          
          return createErrorResponse(`Validation error: ${validation.error}`, 400, origin)
        }

        // Replace request with validated data
        if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
          req = new NextRequest(req.url, {
            method: req.method,
            headers: req.headers,
            body: JSON.stringify(validation.data)
          })
        }
      }

      // Create request context
      const context: RequestContext = {
        user,
        clientIP,
        userAgent,
        startTime,
        requestId
      }

      // Audit logging for API access
      if (config.auditLog && user) {
        await auditLogger.logDataAccess(
          AuditEventType.PROFILE_VIEWED, // Generic data access event
          user.id,
          'API',
          req.nextUrl.pathname,
          clientIP,
          {
            method: req.method,
            request_id: requestId,
            user_agent: userAgent
          }
        )
      }

      // Execute the actual handler
      const response = await handler(req, context)
      
      // Apply security headers
      const secureResponse = applySecurityHeaders(
        applyCORSHeaders(
          applyAPISecurityHeaders(response),
          origin
        )
      )

      // Add request tracking headers
      secureResponse.headers.set('X-Request-ID', requestId)
      secureResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)

      // Log successful API call
      if (config.auditLog && user) {
        await auditLogger.log({
          user_id: user.id,
          event_type: AuditEventType.API_ERROR, // Will be changed to API_SUCCESS when we add that type
          event_description: `API call to ${req.nextUrl.pathname}`,
          risk_level: RiskLevel.LOW,
          ip_address: clientIP,
          user_agent: userAgent,
          request_path: req.nextUrl.pathname,
          request_method: req.method,
          response_status: secureResponse.status,
          response_time: Date.now() - startTime,
          metadata: {
            request_id: requestId
          }
        })
      }

      return secureResponse

    } catch (error) {
      // Log system errors
      await auditLogger.logSystemError(
        error as Error,
        `API route: ${req.nextUrl.pathname}`,
        user?.id,
        {
          request_id: requestId,
          client_ip: clientIP,
          user_agent: userAgent,
          method: req.method
        }
      )

      // Return generic error response (don't expose internal errors)
      return createErrorResponse(
        'Internal server error',
        500,
        origin,
        process.env.NODE_ENV === 'development' ? { error: (error as Error).message } : undefined
      )
    }
  }
}

// Helper function to get client IP
function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for')
  const realIP = req.headers.get('x-real-ip')
  const cfConnectingIP = req.headers.get('cf-connecting-ip')
  
  if (forwarded) return forwarded.split(',')[0].trim()
  if (realIP) return realIP
  if (cfConnectingIP) return cfConnectingIP
  return 'unknown'
}

// Helper function to create error responses
function createErrorResponse(
  message: string,
  status: number,
  origin?: string,
  metadata?: any
): NextResponse {
  const response = NextResponse.json(
    {
      error: message,
      timestamp: new Date().toISOString(),
      ...(metadata && { metadata })
    },
    { status }
  )

  return applySecurityHeaders(
    applyCORSHeaders(
      applyAPISecurityHeaders(response),
      origin
    )
  )
}

// Predefined middleware configurations
export const middlewareConfigs = {
  // Public API endpoints (no auth required)
  public: {
    requireAuth: false,
    auditLog: true,
    allowedMethods: ['GET', 'POST']
  },

  // Protected API endpoints (auth required)
  protected: {
    requireAuth: true,
    requireVerification: true,
    auditLog: true,
    requireCSRF: true
  },

  // Trading endpoints (high security)
  trading: {
    requireAuth: true,
    requireVerification: true,
    auditLog: true,
    requireCSRF: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE']
  },

  // Admin endpoints (highest security)
  admin: {
    requireAuth: true,
    requireVerification: true,
    auditLog: true,
    requireCSRF: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE']
  }
}

// Convenience wrapper functions
export const withPublicSecurity = (handler: any) => 
  withSecurity(handler, middlewareConfigs.public)

export const withProtectedSecurity = (handler: any) => 
  withSecurity(handler, middlewareConfigs.protected)

export const withTradingSecurity = (handler: any) => 
  withSecurity(handler, middlewareConfigs.trading)

export const withAdminSecurity = (handler: any) => 
  withSecurity(handler, middlewareConfigs.admin)
