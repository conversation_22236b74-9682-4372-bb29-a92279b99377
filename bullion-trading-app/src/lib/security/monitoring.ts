import { auditLogger, AuditEventType, RiskLevel } from './auditLogger'
import { SecurityConfig } from './config'

// Security metrics interface
export interface SecurityMetrics {
  timestamp: number
  failedLogins: number
  suspiciousActivities: number
  rateLimitExceeded: number
  maliciousRequests: number
  unauthorizedAccess: number
  systemErrors: number
  activeUsers: number
  totalRequests: number
  averageResponseTime: number
}

// Alert types
export enum AlertType {
  SECURITY_BREACH = 'SECURITY_BREACH',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_ABUSE = 'RATE_LIMIT_ABUSE',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  PERFORMANCE_DEGRADATION = 'PERFORMANCE_DEGRADATION',
  AUTHENTICATION_ANOMALY = 'AUTHENTICATION_ANOMALY',
  DATA_BREACH_ATTEMPT = 'DATA_BREACH_ATTEMPT',
  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION'
}

// Alert severity levels
export enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Alert interface
export interface SecurityAlert {
  id: string
  type: AlertType
  severity: AlertSeverity
  title: string
  description: string
  timestamp: number
  source: string
  metadata: Record<string, any>
  resolved: boolean
  resolvedAt?: number
  resolvedBy?: string
}

// Security monitoring class
export class SecurityMonitor {
  private static instance: SecurityMonitor
  private metrics: SecurityMetrics[] = []
  private alerts: SecurityAlert[] = []
  private metricsInterval: NodeJS.Timeout | null = null
  private alertHandlers: Array<(alert: SecurityAlert) => void> = []

  private constructor() {
    this.startMetricsCollection()
  }

  public static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor()
    }
    return SecurityMonitor.instance
  }

  // Start collecting security metrics
  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(async () => {
      await this.collectMetrics()
    }, 60000) // Collect metrics every minute
  }

  // Collect current security metrics
  private async collectMetrics(): Promise<void> {
    try {
      const now = Date.now()
      const oneMinuteAgo = now - 60000

      // Get audit statistics for the last minute
      const stats = await auditLogger.getAuditStats('day')

      const metrics: SecurityMetrics = {
        timestamp: now,
        failedLogins: this.getEventCount(AuditEventType.LOGIN_FAILED, oneMinuteAgo),
        suspiciousActivities: this.getEventCount(AuditEventType.SUSPICIOUS_ACTIVITY, oneMinuteAgo),
        rateLimitExceeded: this.getEventCount(AuditEventType.RATE_LIMIT_EXCEEDED, oneMinuteAgo),
        maliciousRequests: this.getEventCount(AuditEventType.MALICIOUS_REQUEST, oneMinuteAgo),
        unauthorizedAccess: this.getEventCount(AuditEventType.UNAUTHORIZED_ACCESS, oneMinuteAgo),
        systemErrors: this.getEventCount(AuditEventType.SYSTEM_ERROR, oneMinuteAgo),
        activeUsers: 0, // Would need to implement active user tracking
        totalRequests: 0, // Would need to implement request counting
        averageResponseTime: 0 // Would need to implement response time tracking
      }

      this.metrics.push(metrics)

      // Keep only last 24 hours of metrics
      const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000
      this.metrics = this.metrics.filter(m => m.timestamp > twentyFourHoursAgo)

      // Check for alert conditions
      await this.checkAlertConditions(metrics)

    } catch (error) {
      console.error('Error collecting security metrics:', error)
    }
  }

  // Get count of specific event type in time window
  private getEventCount(eventType: AuditEventType, since: number): number {
    // This would need to query the audit logs
    // For now, return 0 as placeholder
    return 0
  }

  // Check for conditions that should trigger alerts
  private async checkAlertConditions(metrics: SecurityMetrics): Promise<void> {
    const config = SecurityConfig.monitoring.alertThresholds

    // Check failed login threshold
    if (metrics.failedLogins > config.failedLogins) {
      await this.createAlert({
        type: AlertType.AUTHENTICATION_ANOMALY,
        severity: AlertSeverity.HIGH,
        title: 'High Number of Failed Logins',
        description: `${metrics.failedLogins} failed login attempts in the last minute`,
        source: 'SecurityMonitor',
        metadata: { failed_logins: metrics.failedLogins, threshold: config.failedLogins }
      })
    }

    // Check suspicious activity threshold
    if (metrics.suspiciousActivities > config.suspiciousActivity) {
      await this.createAlert({
        type: AlertType.SUSPICIOUS_ACTIVITY,
        severity: AlertSeverity.CRITICAL,
        title: 'Suspicious Activity Detected',
        description: `${metrics.suspiciousActivities} suspicious activities detected in the last minute`,
        source: 'SecurityMonitor',
        metadata: { suspicious_activities: metrics.suspiciousActivities, threshold: config.suspiciousActivity }
      })
    }

    // Check rate limit abuse
    if (metrics.rateLimitExceeded > 20) { // Threshold for rate limit abuse
      await this.createAlert({
        type: AlertType.RATE_LIMIT_ABUSE,
        severity: AlertSeverity.MEDIUM,
        title: 'Rate Limit Abuse Detected',
        description: `${metrics.rateLimitExceeded} rate limit violations in the last minute`,
        source: 'SecurityMonitor',
        metadata: { rate_limit_exceeded: metrics.rateLimitExceeded }
      })
    }

    // Check malicious requests
    if (metrics.maliciousRequests > 0) {
      await this.createAlert({
        type: AlertType.DATA_BREACH_ATTEMPT,
        severity: AlertSeverity.CRITICAL,
        title: 'Malicious Requests Detected',
        description: `${metrics.maliciousRequests} malicious requests blocked in the last minute`,
        source: 'SecurityMonitor',
        metadata: { malicious_requests: metrics.maliciousRequests }
      })
    }

    // Check system errors
    if (metrics.systemErrors > 10) {
      await this.createAlert({
        type: AlertType.SYSTEM_ERROR,
        severity: AlertSeverity.HIGH,
        title: 'High System Error Rate',
        description: `${metrics.systemErrors} system errors in the last minute`,
        source: 'SecurityMonitor',
        metadata: { system_errors: metrics.systemErrors }
      })
    }
  }

  // Create a new security alert
  private async createAlert(alertData: Omit<SecurityAlert, 'id' | 'timestamp' | 'resolved'>): Promise<void> {
    const alert: SecurityAlert = {
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      resolved: false,
      ...alertData
    }

    this.alerts.push(alert)

    // Log the alert
    await auditLogger.logSecurityEvent(
      AuditEventType.SECURITY_VIOLATION,
      `Security alert: ${alert.title}`,
      undefined,
      undefined,
      alert.severity === AlertSeverity.CRITICAL ? RiskLevel.CRITICAL : RiskLevel.HIGH,
      {
        alert_id: alert.id,
        alert_type: alert.type,
        alert_severity: alert.severity,
        ...alert.metadata
      }
    )

    // Notify alert handlers
    this.alertHandlers.forEach(handler => {
      try {
        handler(alert)
      } catch (error) {
        console.error('Error in alert handler:', error)
      }
    })

    // Keep only last 1000 alerts
    if (this.alerts.length > 1000) {
      this.alerts = this.alerts.slice(-1000)
    }
  }

  // Add alert handler
  public addAlertHandler(handler: (alert: SecurityAlert) => void): void {
    this.alertHandlers.push(handler)
  }

  // Remove alert handler
  public removeAlertHandler(handler: (alert: SecurityAlert) => void): void {
    const index = this.alertHandlers.indexOf(handler)
    if (index > -1) {
      this.alertHandlers.splice(index, 1)
    }
  }

  // Get current metrics
  public getCurrentMetrics(): SecurityMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  // Get metrics history
  public getMetricsHistory(hours: number = 24): SecurityMetrics[] {
    const since = Date.now() - hours * 60 * 60 * 1000
    return this.metrics.filter(m => m.timestamp > since)
  }

  // Get active alerts
  public getActiveAlerts(): SecurityAlert[] {
    return this.alerts.filter(a => !a.resolved)
  }

  // Get all alerts
  public getAllAlerts(limit: number = 100): SecurityAlert[] {
    return this.alerts.slice(-limit).reverse()
  }

  // Resolve an alert
  public async resolveAlert(alertId: string, resolvedBy: string): Promise<boolean> {
    const alert = this.alerts.find(a => a.id === alertId)
    if (!alert) return false

    alert.resolved = true
    alert.resolvedAt = Date.now()
    alert.resolvedBy = resolvedBy

    // Log alert resolution
    await auditLogger.log({
      event_type: AuditEventType.ADMIN_ACTION,
      event_description: `Security alert resolved: ${alert.title}`,
      risk_level: RiskLevel.LOW,
      metadata: {
        alert_id: alertId,
        resolved_by: resolvedBy,
        alert_type: alert.type
      }
    })

    return true
  }

  // Get security dashboard data
  public getSecurityDashboard(): {
    currentMetrics: SecurityMetrics | null
    activeAlerts: SecurityAlert[]
    metricsHistory: SecurityMetrics[]
    alertsSummary: Record<AlertType, number>
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  } {
    const currentMetrics = this.getCurrentMetrics()
    const activeAlerts = this.getActiveAlerts()
    const metricsHistory = this.getMetricsHistory(24)

    // Calculate alerts summary
    const alertsSummary = activeAlerts.reduce((acc, alert) => {
      acc[alert.type] = (acc[alert.type] || 0) + 1
      return acc
    }, {} as Record<AlertType, number>)

    // Determine overall risk level
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW'
    
    if (activeAlerts.some(a => a.severity === AlertSeverity.CRITICAL)) {
      riskLevel = 'CRITICAL'
    } else if (activeAlerts.some(a => a.severity === AlertSeverity.HIGH)) {
      riskLevel = 'HIGH'
    } else if (activeAlerts.some(a => a.severity === AlertSeverity.MEDIUM)) {
      riskLevel = 'MEDIUM'
    }

    return {
      currentMetrics,
      activeAlerts,
      metricsHistory,
      alertsSummary,
      riskLevel
    }
  }

  // Stop monitoring
  public stop(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval)
      this.metricsInterval = null
    }
  }
}

// Export singleton instance
export const securityMonitor = SecurityMonitor.getInstance()

// Default alert handlers
export const defaultAlertHandlers = {
  // Console logger
  console: (alert: SecurityAlert) => {
    const timestamp = new Date(alert.timestamp).toISOString()
    console.warn(`[SECURITY ALERT] ${timestamp} - ${alert.severity} - ${alert.title}: ${alert.description}`)
  },

  // Email notification (placeholder)
  email: async (alert: SecurityAlert) => {
    if (SecurityConfig.monitoring.notificationChannels.email) {
      // Implement email notification
      console.log(`Email alert would be sent: ${alert.title}`)
    }
  },

  // Webhook notification (placeholder)
  webhook: async (alert: SecurityAlert) => {
    if (SecurityConfig.monitoring.notificationChannels.webhook) {
      // Implement webhook notification
      console.log(`Webhook alert would be sent: ${alert.title}`)
    }
  }
}

// Initialize default alert handlers
securityMonitor.addAlertHandler(defaultAlertHandlers.console)
securityMonitor.addAlertHandler(defaultAlertHandlers.email)
securityMonitor.addAlertHandler(defaultAlertHandlers.webhook)
