import { NextResponse } from 'next/server'

export interface SecurityHeadersConfig {
  contentSecurityPolicy?: string
  strictTransportSecurity?: string
  xFrameOptions?: string
  xContentTypeOptions?: string
  referrerPolicy?: string
  permissionsPolicy?: string
  crossOriginEmbedderPolicy?: string
  crossOriginOpenerPolicy?: string
  crossOriginResourcePolicy?: string
}

// Default security headers for production
export const defaultSecurityHeaders: SecurityHeadersConfig = {
  // Content Security Policy - prevents XSS attacks
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "media-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.metals.live",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),

  // HSTS - forces HTTPS connections
  strictTransportSecurity: 'max-age=31536000; includeSubDomains; preload',

  // Prevents clickjacking attacks
  xFrameOptions: 'DENY',

  // Prevents MIME type sniffing
  xContentTypeOptions: 'nosniff',

  // Controls referrer information
  referrerPolicy: 'strict-origin-when-cross-origin',

  // Controls browser features
  permissionsPolicy: [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=(self)',
    'usb=()',
    'magnetometer=()',
    'accelerometer=()',
    'gyroscope=()',
    'fullscreen=(self)'
  ].join(', '),

  // Cross-origin policies
  crossOriginEmbedderPolicy: 'require-corp',
  crossOriginOpenerPolicy: 'same-origin',
  crossOriginResourcePolicy: 'same-origin'
}

// Development security headers (more permissive)
export const developmentSecurityHeaders: SecurityHeadersConfig = {
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "font-src 'self' data:",
    "img-src 'self' data: https: blob:",
    "media-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.metals.live ws://localhost:* http://localhost:*",
    "frame-src 'self'",
    "object-src 'none'"
  ].join('; '),

  xFrameOptions: 'SAMEORIGIN',
  xContentTypeOptions: 'nosniff',
  referrerPolicy: 'strict-origin-when-cross-origin'
}

// Apply security headers to response
export function applySecurityHeaders(
  response: NextResponse, 
  config?: SecurityHeadersConfig
): NextResponse {
  const headers = config || (process.env.NODE_ENV === 'production' 
    ? defaultSecurityHeaders 
    : developmentSecurityHeaders)

  // Apply each header if defined
  if (headers.contentSecurityPolicy) {
    response.headers.set('Content-Security-Policy', headers.contentSecurityPolicy)
  }

  if (headers.strictTransportSecurity) {
    response.headers.set('Strict-Transport-Security', headers.strictTransportSecurity)
  }

  if (headers.xFrameOptions) {
    response.headers.set('X-Frame-Options', headers.xFrameOptions)
  }

  if (headers.xContentTypeOptions) {
    response.headers.set('X-Content-Type-Options', headers.xContentTypeOptions)
  }

  if (headers.referrerPolicy) {
    response.headers.set('Referrer-Policy', headers.referrerPolicy)
  }

  if (headers.permissionsPolicy) {
    response.headers.set('Permissions-Policy', headers.permissionsPolicy)
  }

  if (headers.crossOriginEmbedderPolicy) {
    response.headers.set('Cross-Origin-Embedder-Policy', headers.crossOriginEmbedderPolicy)
  }

  if (headers.crossOriginOpenerPolicy) {
    response.headers.set('Cross-Origin-Opener-Policy', headers.crossOriginOpenerPolicy)
  }

  if (headers.crossOriginResourcePolicy) {
    response.headers.set('Cross-Origin-Resource-Policy', headers.crossOriginResourcePolicy)
  }

  // Additional security headers
  response.headers.set('X-DNS-Prefetch-Control', 'off')
  response.headers.set('X-Download-Options', 'noopen')
  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none')
  response.headers.set('X-XSS-Protection', '1; mode=block')

  return response
}

// CORS configuration
export interface CORSConfig {
  origin?: string | string[] | boolean
  methods?: string[]
  allowedHeaders?: string[]
  exposedHeaders?: string[]
  credentials?: boolean
  maxAge?: number
  preflightContinue?: boolean
  optionsSuccessStatus?: number
}

export const defaultCORSConfig: CORSConfig = {
  origin: process.env.NODE_ENV === 'production' 
    ? [process.env.NEXT_PUBLIC_APP_URL || 'https://yourdomain.com']
    : true, // Allow all origins in development
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'X-File-Name'
  ],
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
    'X-Total-Count'
  ],
  credentials: true,
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200
}

// Apply CORS headers to response
export function applyCORSHeaders(
  response: NextResponse,
  origin?: string,
  config?: CORSConfig
): NextResponse {
  const corsConfig = { ...defaultCORSConfig, ...config }

  // Handle origin
  if (corsConfig.origin === true) {
    response.headers.set('Access-Control-Allow-Origin', origin || '*')
  } else if (typeof corsConfig.origin === 'string') {
    response.headers.set('Access-Control-Allow-Origin', corsConfig.origin)
  } else if (Array.isArray(corsConfig.origin) && origin) {
    if (corsConfig.origin.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin)
    }
  }

  // Handle methods
  if (corsConfig.methods) {
    response.headers.set('Access-Control-Allow-Methods', corsConfig.methods.join(', '))
  }

  // Handle allowed headers
  if (corsConfig.allowedHeaders) {
    response.headers.set('Access-Control-Allow-Headers', corsConfig.allowedHeaders.join(', '))
  }

  // Handle exposed headers
  if (corsConfig.exposedHeaders) {
    response.headers.set('Access-Control-Expose-Headers', corsConfig.exposedHeaders.join(', '))
  }

  // Handle credentials
  if (corsConfig.credentials) {
    response.headers.set('Access-Control-Allow-Credentials', 'true')
  }

  // Handle max age
  if (corsConfig.maxAge) {
    response.headers.set('Access-Control-Max-Age', corsConfig.maxAge.toString())
  }

  return response
}

// Create a secure response with all headers applied
export function createSecureResponse(
  data?: any,
  init?: ResponseInit,
  origin?: string,
  securityConfig?: SecurityHeadersConfig,
  corsConfig?: CORSConfig
): NextResponse {
  const response = data 
    ? NextResponse.json(data, init)
    : NextResponse.next(init)

  // Apply security headers
  applySecurityHeaders(response, securityConfig)

  // Apply CORS headers
  applyCORSHeaders(response, origin, corsConfig)

  return response
}

// Middleware helper for handling preflight requests
export function handlePreflightRequest(origin?: string): NextResponse {
  const response = new NextResponse(null, { status: 200 })
  
  applyCORSHeaders(response, origin)
  applySecurityHeaders(response)
  
  return response
}

// Security headers for API routes
export const apiSecurityHeaders = {
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
  'Surrogate-Control': 'no-store'
}

// Apply API-specific security headers
export function applyAPISecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(apiSecurityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  return response
}
