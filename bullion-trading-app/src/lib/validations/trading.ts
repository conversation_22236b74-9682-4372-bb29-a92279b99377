import { z } from 'zod'

// Trade order schema
export const tradeOrderSchema = z.object({
  product_id: z.string().min(1, 'Product selection is required'),
  type: z.enum(['buy', 'sell'], {
    required_error: 'Trade type is required',
  }),
  quantity: z
    .number()
    .min(0.001, 'Quantity must be greater than 0')
    .max(1000000, 'Quantity is too large'),
  price_per_unit: z
    .number()
    .min(0.01, 'Price must be greater than 0')
    .max(1000000, 'Price is too high'),
})

// Price alert schema
export const priceAlertSchema = z.object({
  product_id: z.string().min(1, 'Product selection is required'),
  target_price: z
    .number()
    .min(0.01, 'Target price must be greater than 0')
    .max(1000000, 'Target price is too high'),
  condition: z.enum(['above', 'below'], {
    required_error: 'Alert condition is required',
  }),
})

// Portfolio filter schema
export const portfolioFilterSchema = z.object({
  product_type: z.enum(['gold', 'silver', 'platinum', 'palladium']).optional(),
  sort_by: z.enum(['value', 'profit_loss', 'quantity', 'name']).optional(),
  sort_order: z.enum(['asc', 'desc']).optional(),
})

// Trade history filter schema
export const tradeHistoryFilterSchema = z.object({
  product_id: z.string().optional(),
  type: z.enum(['buy', 'sell']).optional(),
  status: z.enum(['pending', 'completed', 'cancelled', 'failed']).optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
})

// Watchlist schema
export const watchlistSchema = z.object({
  product_id: z.string().min(1, 'Product selection is required'),
})

// Type exports
export type TradeOrderFormData = z.infer<typeof tradeOrderSchema>
export type PriceAlertFormData = z.infer<typeof priceAlertSchema>
export type PortfolioFilterData = z.infer<typeof portfolioFilterSchema>
export type TradeHistoryFilterData = z.infer<typeof tradeHistoryFilterSchema>
export type WatchlistFormData = z.infer<typeof watchlistSchema>
