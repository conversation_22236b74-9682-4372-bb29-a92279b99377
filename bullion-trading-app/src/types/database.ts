export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      audit_logs: {
        Row: {
          id: string
          user_id: string | null
          action: string
          table_name: string | null
          record_id: string | null
          old_values: Json | null
          new_values: Json | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          action: string
          table_name?: string | null
          record_id?: string | null
          old_values?: Json | null
          new_values?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          action?: string
          table_name?: string | null
          record_id?: string | null
          old_values?: Json | null
          new_values?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
      }
      bullion_products: {
        Row: {
          id: string
          name: string
          symbol: string
          type: 'gold' | 'silver' | 'platinum' | 'palladium'
          purity: string
          weight: number
          weight_unit: 'oz' | 'gram' | 'kg'
          current_price: number
          price_currency: string
          price_per_unit: string
          image_url: string | null
          description: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          symbol: string
          type: 'gold' | 'silver' | 'platinum' | 'palladium'
          purity: string
          weight: number
          weight_unit: 'oz' | 'gram' | 'kg'
          current_price?: number
          price_currency?: string
          price_per_unit: string
          image_url?: string | null
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          symbol?: string
          type?: 'gold' | 'silver' | 'platinum' | 'palladium'
          purity?: string
          weight?: number
          weight_unit?: 'oz' | 'gram' | 'kg'
          current_price?: number
          price_currency?: string
          price_per_unit?: string
          image_url?: string | null
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      otp_verifications: {
        Row: {
          id: string
          user_id: string | null
          phone: string
          otp_code: string
          otp_type: 'registration' | 'login' | 'password_reset' | 'phone_verification'
          is_verified: boolean
          attempts: number
          expires_at: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          phone: string
          otp_code: string
          otp_type: 'registration' | 'login' | 'password_reset' | 'phone_verification'
          is_verified?: boolean
          attempts?: number
          expires_at: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          phone?: string
          otp_code?: string
          otp_type?: 'registration' | 'login' | 'password_reset' | 'phone_verification'
          is_verified?: boolean
          attempts?: number
          expires_at?: string
          created_at?: string
        }
      }
      portfolios: {
        Row: {
          id: string
          user_id: string
          product_id: string
          quantity: number
          average_buy_price: number
          total_invested: number
          current_value: number
          profit_loss: number
          profit_loss_percentage: number
          last_updated: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          quantity?: number
          average_buy_price?: number
          total_invested?: number
          current_value?: number
          profit_loss?: number
          profit_loss_percentage?: number
          last_updated?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          quantity?: number
          average_buy_price?: number
          total_invested?: number
          current_value?: number
          profit_loss?: number
          profit_loss_percentage?: number
          last_updated?: string
          created_at?: string
        }
      }
      price_alerts: {
        Row: {
          id: string
          user_id: string
          product_id: string
          target_price: number
          condition: 'above' | 'below'
          is_active: boolean
          triggered_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          target_price: number
          condition: 'above' | 'below'
          is_active?: boolean
          triggered_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          target_price?: number
          condition?: 'above' | 'below'
          is_active?: boolean
          triggered_at?: string | null
          created_at?: string
        }
      }
      price_history: {
        Row: {
          id: string
          product_id: string
          price: number
          currency: string
          source: string
          timestamp: string
          created_at: string
        }
        Insert: {
          id?: string
          product_id: string
          price: number
          currency?: string
          source?: string
          timestamp?: string
          created_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          price?: number
          currency?: string
          source?: string
          timestamp?: string
          created_at?: string
        }
      }
      receipts: {
        Row: {
          id: string
          transaction_id: string
          user_id: string
          receipt_number: string
          amount: number
          fees: number
          tax: number
          total_amount: number
          payment_method: string
          status: string
          pdf_url: string | null
          generated_at: string
          created_at: string
        }
        Insert: {
          id?: string
          transaction_id: string
          user_id: string
          receipt_number: string
          amount: number
          fees?: number
          tax?: number
          total_amount: number
          payment_method?: string
          status?: string
          pdf_url?: string | null
          generated_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          transaction_id?: string
          user_id?: string
          receipt_number?: string
          amount?: number
          fees?: number
          tax?: number
          total_amount?: number
          payment_method?: string
          status?: string
          pdf_url?: string | null
          generated_at?: string
          created_at?: string
        }
      }
      trades: {
        Row: {
          id: string
          user_id: string
          product_id: string
          type: 'buy' | 'sell'
          quantity: number
          price_per_unit: number
          total_amount: number
          status: 'pending' | 'completed' | 'cancelled' | 'failed'
          fees: number
          notes: string | null
          created_at: string
          updated_at: string
          completed_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          type: 'buy' | 'sell'
          quantity: number
          price_per_unit: number
          total_amount: number
          status?: 'pending' | 'completed' | 'cancelled' | 'failed'
          fees?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
          completed_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          type?: 'buy' | 'sell'
          quantity?: number
          price_per_unit?: number
          total_amount?: number
          status?: 'pending' | 'completed' | 'cancelled' | 'failed'
          fees?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
          completed_at?: string | null
        }
      }
      users: {
        Row: {
          id: string
          email: string
          phone: string | null
          full_name: string
          avatar_url: string | null
          is_verified: boolean
          is_phone_verified: boolean
          two_factor_enabled: boolean
          status: 'active' | 'inactive' | 'suspended'
          login_attempts: number
          locked_until: string | null
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          phone?: string | null
          full_name: string
          avatar_url?: string | null
          is_verified?: boolean
          is_phone_verified?: boolean
          two_factor_enabled?: boolean
          status?: 'active' | 'inactive' | 'suspended'
          login_attempts?: number
          locked_until?: string | null
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          phone?: string | null
          full_name?: string
          avatar_url?: string | null
          is_verified?: boolean
          is_phone_verified?: boolean
          two_factor_enabled?: boolean
          status?: 'active' | 'inactive' | 'suspended'
          login_attempts?: number
          locked_until?: string | null
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      watchlists: {
        Row: {
          id: string
          user_id: string
          product_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      cleanup_expired_otps: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      get_user_portfolio_summary: {
        Args: {
          user_uuid: string
        }
        Returns: {
          total_value: number
          total_invested: number
          total_profit_loss: number
          total_profit_loss_percentage: number
          product_count: number
        }[]
      }
    }
    Enums: {
      alert_condition: 'above' | 'below'
      bullion_type: 'gold' | 'silver' | 'platinum' | 'palladium'
      otp_type: 'registration' | 'login' | 'password_reset' | 'phone_verification'
      trade_status: 'pending' | 'completed' | 'cancelled' | 'failed'
      trade_type: 'buy' | 'sell'
      user_status: 'active' | 'inactive' | 'suspended'
      weight_unit: 'oz' | 'gram' | 'kg'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
