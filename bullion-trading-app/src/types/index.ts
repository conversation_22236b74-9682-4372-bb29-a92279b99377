// User and Authentication Types
export interface User {
  id: string
  email: string
  phone?: string
  full_name: string
  avatar_url?: string
  is_verified: boolean
  is_phone_verified: boolean
  two_factor_enabled: boolean
  created_at: string
  updated_at: string
}

export interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
}

export interface LoginCredentials {
  email: string
  password: string
  remember_me?: boolean
}

export interface RegisterCredentials {
  email: string
  phone?: string
  password: string
  full_name: string
  confirm_password: string
}

export interface OTPVerification {
  phone: string
  otp: string
  type: 'registration' | 'login' | 'password_reset'
}

// Bullion and Trading Types
export interface BullionProduct {
  id: string
  name: string
  symbol: string
  type: 'gold' | 'silver' | 'platinum' | 'palladium'
  purity: string
  weight: number
  weight_unit: 'oz' | 'gram' | 'kg'
  current_price: number
  price_currency: string
  price_per_unit: string
  image_url?: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface PriceData {
  id: string
  product_id: string
  price: number
  currency: string
  timestamp: string
  source: string
}

export interface Trade {
  id: string
  user_id: string
  product_id: string
  type: 'buy' | 'sell'
  quantity: number
  price_per_unit: number
  total_amount: number
  status: 'pending' | 'completed' | 'cancelled' | 'failed'
  created_at: string
  updated_at: string
  completed_at?: string
}

export interface Portfolio {
  id: string
  user_id: string
  product_id: string
  quantity: number
  average_buy_price: number
  current_value: number
  profit_loss: number
  profit_loss_percentage: number
  last_updated: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  has_more: boolean
}

// Chart and Analytics Types
export interface ChartDataPoint {
  timestamp: string
  price: number
  volume?: number
}

export interface PriceAlert {
  id: string
  user_id: string
  product_id: string
  target_price: number
  condition: 'above' | 'below'
  is_active: boolean
  created_at: string
  triggered_at?: string
}

// WhatsApp API Types
export interface WhatsAppMessage {
  to: string
  message: string
  template_id?: string
  variables?: Record<string, string>
}

export interface WhatsAppResponse {
  message_status: 'Success' | 'Error'
  data?: {
    from: string
    to: string
    status_code: number
  }
  error?: string
}
