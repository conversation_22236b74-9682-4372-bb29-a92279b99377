'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useOrders } from '@/hooks/useOrders'
import { OrderService } from '@/lib/services/orderService'
import { cn } from '@/lib/utils'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  Weight,
  MoreHorizontal,
  Eye,
  X,
  RefreshCw,
  Loader2,
  Play,
  Trash2
} from 'lucide-react'

export interface Order {
  id: string
  product_name: string
  product_id: string
  metal_type: 'gold' | 'silver' | 'platinum' | 'palladium'
  order_type: 'buy' | 'sell'
  order_method: 'market' | 'limit'
  quantity: number
  price_per_unit: number
  limit_price?: number
  total_amount: number
  status: 'pending' | 'filled' | 'cancelled' | 'partially_filled'
  created_at: string
  updated_at: string
  filled_quantity?: number
  filled_price?: number
  fees?: number
}

interface OrderManagementProps {
  initialFilter?: 'all' | 'pending' | 'completed' | 'cancelled'
  onViewOrder?: (orderId: string) => void
  className?: string
}

export function OrderManagement({
  initialFilter = 'all',
  onViewOrder,
  className
}: OrderManagementProps) {
  const [selectedTab, setSelectedTab] = useState(initialFilter)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Use the orders hook with filtering
  const {
    orders,
    loading,
    error,
    stats,
    cancelOrder,
    executeOrder,
    deleteOrder,
    refreshOrders,
    loadMore,
    hasMore
  } = useOrders({
    status: selectedTab === 'all' ? undefined : selectedTab as any,
    limit: 20
  })

  // Filter orders based on selected tab
  const filteredOrders = orders.filter(order => {
    switch (selectedTab) {
      case 'pending':
        return order.status === 'pending'
      case 'completed':
        return order.status === 'completed'
      case 'cancelled':
        return order.status === 'cancelled'
      default:
        return true
    }
  })

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    return OrderService.getStatusColor(status as any)
  }

  // Action handlers
  const handleCancelOrder = async (orderId: string) => {
    setActionLoading(orderId)
    try {
      const { success, error } = await cancelOrder(orderId)
      if (!success) {
        console.error('Failed to cancel order:', error)
      }
    } catch (err) {
      console.error('Error cancelling order:', err)
    } finally {
      setActionLoading(null)
    }
  }

  const handleExecuteOrder = async (orderId: string) => {
    setActionLoading(orderId)
    try {
      const { success, error } = await executeOrder(orderId)
      if (!success) {
        console.error('Failed to execute order:', error)
      }
    } catch (err) {
      console.error('Error executing order:', err)
    } finally {
      setActionLoading(null)
    }
  }

  const handleDeleteOrder = async (orderId: string) => {
    setActionLoading(orderId)
    try {
      const { success, error } = await deleteOrder(orderId)
      if (!success) {
        console.error('Failed to delete order:', error)
      }
    } catch (err) {
      console.error('Error deleting order:', err)
    } finally {
      setActionLoading(null)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      case 'failed':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getMetalColor = (metal: string) => {
    switch (metal) {
      case 'gold': return 'bg-yellow-100 text-yellow-800'
      case 'silver': return 'bg-gray-100 text-gray-800'
      case 'platinum': return 'bg-blue-100 text-blue-800'
      case 'palladium': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const OrderCard = ({ order }: { order: any }) => {
    const formattedOrder = OrderService.formatOrder(order)

    return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-medium text-gray-900">
                {order.bullion_products?.name || 'Product'}
              </h3>
              <Badge variant="secondary" className={getMetalColor(order.bullion_products?.type || 'gold')}>
                {order.bullion_products?.type || 'gold'}
              </Badge>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                {order.type === 'buy' ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span className={formattedOrder.type_color}>
                  {order.type.toUpperCase()}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formattedOrder.formatted_date}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge className={formattedOrder.status_color}>
              {getStatusIcon(order.status)}
              <span className="ml-1">{order.status.replace('_', ' ')}</span>
            </Badge>
            
            <div className="flex items-center gap-1">
              {onViewOrder && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onViewOrder(order.id)}
                  className="p-1"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}

              {order.status === 'pending' && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleExecuteOrder(order.id)}
                    disabled={actionLoading === order.id}
                    className="p-1 text-green-600 hover:text-green-700"
                  >
                    {actionLoading === order.id ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCancelOrder(order.id)}
                    disabled={actionLoading === order.id}
                    className="p-1 text-red-600 hover:text-red-700"
                  >
                    {actionLoading === order.id ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <X className="h-4 w-4" />
                    )}
                  </Button>
                </>
              )}

              {(order.status === 'cancelled' || order.status === 'failed') && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteOrder(order.id)}
                  disabled={actionLoading === order.id}
                  className="p-1 text-gray-600 hover:text-gray-700"
                >
                  {actionLoading === order.id ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="block text-gray-500 mb-1">Quantity</span>
            <span className="font-medium">
              {order.quantity} units
            </span>
          </div>

          <div>
            <span className="block text-gray-500 mb-1">Price</span>
            <span className="font-medium">
              {formattedOrder.formatted_price}
            </span>
          </div>

          <div>
            <span className="block text-gray-500 mb-1">Total Amount</span>
            <span className="font-medium">{formattedOrder.formatted_amount}</span>
          </div>
          
          <div>
            <span className="block text-gray-500 mb-1">Fees</span>
            <span className="font-medium">
              {formattedOrder.formatted_fees}
            </span>
          </div>
        </div>

        {order.status === 'completed' && order.fees && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Transaction Fees:</span>
              <span className="font-medium">{formattedOrder.formatted_fees}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
    )
  }



  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Order Management</h2>
          <p className="text-gray-600">Track and manage your trading orders</p>
        </div>

        <Button
          variant="outline"
          onClick={refreshOrders}
          disabled={loading}
        >
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      {/* Error State */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Stats */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-gray-900">{stats.total_orders}</div>
              <div className="text-sm text-gray-600">Total Orders</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending_orders}</div>
              <div className="text-sm text-gray-600">Pending</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.completed_orders}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-gray-600">
                {formatPrice(stats.total_volume)}
              </div>
              <div className="text-sm text-gray-600">Total Volume</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Order Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <MoreHorizontal className="h-4 w-4" />
            All ({orders.length})
          </TabsTrigger>
          <TabsTrigger value="pending" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Pending ({stats?.pending_orders || 0})
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Completed ({stats?.completed_orders || 0})
          </TabsTrigger>
          <TabsTrigger value="cancelled" className="flex items-center gap-2">
            <XCircle className="h-4 w-4" />
            Cancelled ({stats?.cancelled_orders || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab} className="space-y-4">
          {loading && filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Loader2 className="h-8 w-8 text-gray-400 mb-4 animate-spin" />
                <p className="text-gray-600">Loading orders...</p>
              </CardContent>
            </Card>
          ) : filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Clock className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Orders Found
                </h3>
                <p className="text-gray-600 text-center">
                  {selectedTab === 'all'
                    ? "You haven't placed any orders yet."
                    : `No ${selectedTab} orders found.`
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {/* Order Cards */}
              <div className="space-y-4">
                {filteredOrders.map((order) => (
                  <OrderCard key={order.id} order={order} />
                ))}
              </div>
            </div>
          )}

          {/* Load More Button */}
          {hasMore && filteredOrders.length > 0 && (
            <div className="flex justify-center pt-4">
              <Button
                variant="outline"
                onClick={loadMore}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Load More Orders'
                )}
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
