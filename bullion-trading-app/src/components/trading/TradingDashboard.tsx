'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { PriceDashboard } from '@/components/prices/PriceDashboard'
import { OrderForm, OrderData } from './OrderForm'
import { OrderManagement } from './OrderManagement'
import { TransactionHistory } from '@/components/transactions/TransactionHistory'
import { Portfolio } from '../portfolio/Portfolio'
import { Reports } from '../reports/Reports'
import { Product } from '@/components/products/ProductCard'
import { cn } from '@/lib/utils'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus,
  BarChart3,
  Wallet,
  ShoppingCart,
  History
} from 'lucide-react'

interface TradingStats {
  total_orders: number
  active_orders: number
  completed_orders: number
  total_volume: number
  profit_loss: number
  profit_loss_percent: number
}

interface TradingDashboardProps {
  stats?: TradingStats
  availableProducts?: Product[]
  onPlaceOrder?: (orderData: OrderData) => void
  className?: string
}

// Mock data for demonstration
const mockStats: TradingStats = {
  total_orders: 24,
  active_orders: 3,
  completed_orders: 21,
  total_volume: 2450000,
  profit_loss: 125000,
  profit_loss_percent: 5.4
}

export function TradingDashboard({
  stats = mockStats,
  availableProducts = [],
  onPlaceOrder,
  className
}: TradingDashboardProps) {
  const [selectedTab, setSelectedTab] = useState('overview')
  const [showOrderForm, setShowOrderForm] = useState(false)
  const [orderType, setOrderType] = useState<'buy' | 'sell'>('buy')
  const [selectedProduct, setSelectedProduct] = useState<Product | undefined>()

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const handlePlaceOrder = async (orderData: OrderData) => {
    try {
      await onPlaceOrder?.(orderData)
      setShowOrderForm(false)
      setSelectedProduct(undefined)
    } catch (error) {
      console.error('Error placing order:', error)
    }
  }

  const handleQuickBuy = (product: Product) => {
    setSelectedProduct(product)
    setOrderType('buy')
    setShowOrderForm(true)
  }

  const handleQuickSell = (product: Product) => {
    setSelectedProduct(product)
    setOrderType('sell')
    setShowOrderForm(true)
  }

  const StatCard = ({ 
    title, 
    value, 
    change, 
    changePercent, 
    icon: Icon, 
    trend 
  }: {
    title: string
    value: string | number
    change?: number
    changePercent?: number
    icon: React.ComponentType<{ className?: string }>
    trend?: 'up' | 'down' | 'neutral'
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {change !== undefined && changePercent !== undefined && (
              <div className={cn(
                'flex items-center text-sm mt-1',
                trend === 'up' ? 'text-green-600' : 
                trend === 'down' ? 'text-red-600' : 'text-gray-600'
              )}>
                {trend === 'up' && <TrendingUp className="h-3 w-3 mr-1" />}
                {trend === 'down' && <TrendingDown className="h-3 w-3 mr-1" />}
                <span>
                  {change > 0 ? '+' : ''}{formatPrice(change)} ({changePercent > 0 ? '+' : ''}{changePercent}%)
                </span>
              </div>
            )}
          </div>
          <div className="p-3 bg-amber-50 rounded-full">
            <Icon className="h-6 w-6 text-amber-600" />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Trading Dashboard</h1>
          <p className="text-gray-600">Monitor your trading activity and manage orders</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => {
              setOrderType('sell')
              setShowOrderForm(true)
            }}
          >
            <TrendingDown className="h-4 w-4 mr-2" />
            Sell
          </Button>
          <Button
            onClick={() => {
              setOrderType('buy')
              setShowOrderForm(true)
            }}
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Buy
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Orders"
          value={stats.total_orders}
          icon={Activity}
        />
        <StatCard
          title="Active Orders"
          value={stats.active_orders}
          icon={Clock}
        />
        <StatCard
          title="Total Volume"
          value={formatPrice(stats.total_volume)}
          icon={BarChart3}
        />
        <StatCard
          title="P&L"
          value={formatPrice(stats.profit_loss)}
          change={stats.profit_loss}
          changePercent={stats.profit_loss_percent}
          icon={TrendingUp}
          trend={stats.profit_loss >= 0 ? 'up' : 'down'}
        />
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="transactions">History</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="markets">Markets</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Live Prices */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Live Market Prices</h2>
            <PriceDashboard />
          </div>



          {/* Quick Actions */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="cursor-pointer hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6 text-center">
                  <div className="p-3 bg-green-100 rounded-full w-fit mx-auto mb-3">
                    <ShoppingCart className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-2">Quick Buy</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Purchase precious metals at market price
                  </p>
                  <Button
                    size="sm"
                    onClick={() => {
                      setOrderType('buy')
                      setShowOrderForm(true)
                    }}
                  >
                    Buy Now
                  </Button>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6 text-center">
                  <div className="p-3 bg-red-100 rounded-full w-fit mx-auto mb-3">
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-2">Quick Sell</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Sell your holdings at current rates
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setOrderType('sell')
                      setShowOrderForm(true)
                    }}
                  >
                    Sell Now
                  </Button>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-6 text-center">
                  <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-3">
                    <Wallet className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-2">Portfolio</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    View your holdings and performance
                  </p>
                  <Button variant="outline" size="sm">
                    View Portfolio
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="portfolio">
          <Portfolio />
        </TabsContent>

        <TabsContent value="orders">
          <OrderManagement
            initialFilter="all"
            onViewOrder={(orderId) => console.log('View order:', orderId)}
          />
        </TabsContent>

        <TabsContent value="transactions">
          <TransactionHistory
            showFilters={true}
            showStats={true}
            initialFilter="all"
          />
        </TabsContent>

        <TabsContent value="reports">
          <Reports />
        </TabsContent>

        <TabsContent value="markets">
          <div className="space-y-6">
            <PriceDashboard />
            
            {/* Market Analysis placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>Market Analysis</CardTitle>
                <CardDescription>
                  Technical analysis and market insights
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Market Analysis Coming Soon
                    </h3>
                    <p className="text-gray-600">
                      Advanced charting and technical analysis tools will be available here.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Order Form Modal */}
      {showOrderForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <OrderForm
              product={selectedProduct}
              orderType={orderType}
              onSubmit={handlePlaceOrder}
              onCancel={() => {
                setShowOrderForm(false)
                setSelectedProduct(undefined)
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}
