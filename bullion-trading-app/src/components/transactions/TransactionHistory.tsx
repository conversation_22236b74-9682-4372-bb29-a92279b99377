'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useTransactions } from '@/hooks/useTransactions'
import { TransactionService } from '@/lib/services/transactionService'
import { cn } from '@/lib/utils'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  Weight,
  Download,
  Receipt,
  Filter,
  RefreshCw,
  Loader2,
  FileText,
  Search,
  ArrowUpDown
} from 'lucide-react'

interface TransactionHistoryProps {
  className?: string
  showFilters?: boolean
  showStats?: boolean
  initialFilter?: 'all' | 'completed' | 'pending' | 'cancelled'
}

export function TransactionHistory({
  className,
  showFilters = true,
  showStats = true,
  initialFilter = 'all'
}: TransactionHistoryProps) {
  const [selectedTab, setSelectedTab] = useState(initialFilter)
  const [searchTerm, setSearchTerm] = useState('')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [productFilter, setProductFilter] = useState('all')
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // Use the transactions hook with filtering
  const {
    transactions,
    loading,
    error,
    stats,
    total,
    hasMore,
    refreshTransactions,
    loadMore,
    generateReceipt,
    downloadReceipt,
    exportTransactions
  } = useTransactions({
    status: selectedTab === 'all' ? undefined : selectedTab as any,
    product_id: productFilter === 'all' ? undefined : productFilter,
    date_from: dateFrom || undefined,
    date_to: dateTo || undefined,
    limit: 20,
    autoRefresh: true
  })

  // Filter transactions by search term
  const filteredTransactions = transactions.filter(transaction => {
    if (!searchTerm) return true
    
    const searchLower = searchTerm.toLowerCase()
    return (
      transaction.bullion_products?.name.toLowerCase().includes(searchLower) ||
      transaction.bullion_products?.symbol.toLowerCase().includes(searchLower) ||
      transaction.id.toLowerCase().includes(searchLower) ||
      transaction.type.toLowerCase().includes(searchLower)
    )
  })

  const handleGenerateReceipt = async (transactionId: string) => {
    setActionLoading(`receipt-${transactionId}`)
    try {
      const result = await generateReceipt(transactionId)
      if (!result.success) {
        console.error('Failed to generate receipt:', result.error)
      }
    } finally {
      setActionLoading(null)
    }
  }

  const handleDownloadReceipt = async (receiptId: string) => {
    setActionLoading(`download-${receiptId}`)
    try {
      const result = await downloadReceipt(receiptId)
      if (!result.success) {
        console.error('Failed to download receipt:', result.error)
      }
    } finally {
      setActionLoading(null)
    }
  }

  const handleExportTransactions = async () => {
    setActionLoading('export')
    try {
      const result = await exportTransactions()
      if (!result.success) {
        console.error('Failed to export transactions:', result.error)
      }
    } finally {
      setActionLoading(null)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      case 'failed':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getTypeIcon = (type: string) => {
    return type === 'buy' ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Statistics Cards */}
      {showStats && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Volume</p>
                  <p className="text-2xl font-bold">
                    ₹{stats.total_volume.toLocaleString('en-IN')}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Transactions</p>
                  <p className="text-2xl font-bold">{stats.total_transactions}</p>
                </div>
                <FileText className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Fees</p>
                  <p className="text-2xl font-bold">
                    ₹{stats.total_fees.toLocaleString('en-IN')}
                  </p>
                </div>
                <Receipt className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">P&L</p>
                  <p className={cn(
                    "text-2xl font-bold",
                    stats.profit_loss >= 0 ? "text-green-600" : "text-red-600"
                  )}>
                    ₹{stats.profit_loss.toLocaleString('en-IN')}
                  </p>
                </div>
                <ArrowUpDown className={cn(
                  "h-8 w-8",
                  stats.profit_loss >= 0 ? "text-green-600" : "text-red-600"
                )} />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>
                View and manage your trading transactions
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshTransactions}
                disabled={loading}
              >
                <RefreshCw className={cn("h-4 w-4 mr-2", loading && "animate-spin")} />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportTransactions}
                disabled={actionLoading === 'export'}
              >
                {actionLoading === 'export' ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Export
              </Button>
            </div>
          </div>
        </CardHeader>

        {showFilters && (
          <CardContent className="border-b">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Input
                type="date"
                placeholder="From date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
              
              <Input
                type="date"
                placeholder="To date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
              
              <Select value={productFilter} onValueChange={setProductFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All products" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Products</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                  <SelectItem value="palladium">Palladium</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        )}

        <CardContent className="p-0">
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="w-full justify-start rounded-none border-b">
              <TabsTrigger value="all">All ({total})</TabsTrigger>
              <TabsTrigger value="completed">
                Completed ({stats?.completed_transactions || 0})
              </TabsTrigger>
              <TabsTrigger value="pending">
                Pending ({stats?.pending_transactions || 0})
              </TabsTrigger>
              <TabsTrigger value="cancelled">
                Cancelled ({stats?.cancelled_transactions || 0})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-0">
              {error && (
                <Alert className="m-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {loading && transactions.length === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : filteredTransactions.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
                  <p className="text-gray-500">
                    {searchTerm ? 'Try adjusting your search criteria' : 'Your transactions will appear here'}
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {filteredTransactions.map((transaction) => {
                    const formatted = TransactionService.formatTransaction(transaction)
                    
                    return (
                      <div key={transaction.id} className="p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                              {getTypeIcon(transaction.type)}
                              {getStatusIcon(transaction.status)}
                            </div>
                            
                            <div>
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium">
                                  {transaction.bullion_products?.name}
                                </h4>
                                <Badge variant="outline" className={formatted.status_color}>
                                  {transaction.status}
                                </Badge>
                              </div>
                              <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                                <span>{transaction.quantity} units</span>
                                <span>@{formatted.formatted_price}</span>
                                <span>{formatted.formatted_date}</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="text-right">
                            <div className={cn(
                              "font-semibold",
                              transaction.type === 'buy' ? "text-red-600" : "text-green-600"
                            )}>
                              {transaction.type === 'buy' ? '-' : '+'}{formatted.formatted_amount}
                            </div>
                            <div className="text-sm text-gray-500">
                              Fees: {formatted.formatted_fees}
                            </div>
                          </div>
                        </div>
                        
                        {transaction.status === 'completed' && (
                          <div className="flex items-center justify-end space-x-2 mt-3">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleGenerateReceipt(transaction.id)}
                              disabled={actionLoading === `receipt-${transaction.id}`}
                            >
                              {actionLoading === `receipt-${transaction.id}` ? (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              ) : (
                                <Receipt className="h-4 w-4 mr-2" />
                              )}
                              Receipt
                            </Button>
                          </div>
                        )}
                      </div>
                    )
                  })}
                  
                  {hasMore && (
                    <div className="p-4 text-center">
                      <Button
                        variant="outline"
                        onClick={loadMore}
                        disabled={loading}
                      >
                        {loading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : null}
                        Load More
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
