'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Wallet,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Award,
  Target,
  RefreshCw,
  Download,
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react'
import { usePortfolio } from '@/hooks/usePortfolio'
import { PortfolioService } from '@/lib/services/portfolioService'

// Types
interface StatCardProps {
  title: string
  value: string | number
  change?: number
  changePercent?: number
  icon: React.ComponentType<{ className?: string }>
  trend?: 'up' | 'down' | 'neutral'
  description?: string
  className?: string
}

interface PortfolioOverviewProps {
  className?: string
}

export function PortfolioOverview({ className }: PortfolioOverviewProps) {
  const [showValues, setShowValues] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const { holdings, stats, loading, error, refreshPortfolio, refreshValues, exportPortfolio } = usePortfolio()

  const handleRefresh = async () => {
    setRefreshing(true)
    await refreshPortfolio()
    setRefreshing(false)
  }

  const handleRefreshValues = async () => {
    setRefreshing(true)
    await refreshValues()
    setRefreshing(false)
  }

  const formatPrice = (price: number) => {
    if (!showValues) return '••••••'
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`
  }

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading portfolio...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("space-y-6", className)}>
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!stats || holdings.length === 0) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Wallet className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Holdings Found</h3>
            <p className="text-muted-foreground text-center">
              Start trading to build your precious metals portfolio
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

// StatCard Component
const StatCard = ({ title, value, change, changePercent, icon: Icon, trend = 'neutral', description, className }: StatCardProps) => {
  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4" />
      case 'down':
        return <TrendingDown className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="h-5 w-5 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">{title}</span>
          </div>
          {getTrendIcon() && (
            <div className={cn("flex items-center space-x-1", getTrendColor())}>
              {getTrendIcon()}
            </div>
          )}
        </div>
        <div className="mt-2">
          <div className="text-2xl font-bold">{typeof value === 'number' ? formatPrice(value) : value}</div>
          {(change !== undefined || changePercent !== undefined) && (
            <div className={cn("flex items-center space-x-1 text-sm", getTrendColor())}>
              {change !== undefined && <span>{change >= 0 ? '+' : ''}{formatPrice(change)}</span>}
              {changePercent !== undefined && <span>({formatPercent(changePercent)})</span>}
            </div>
          )}
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

      {/* Portfolio Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Portfolio Value"
          value={formatPrice(stats.total_value)}
          change={stats.day_change}
          changePercent={stats.day_change_percent}
          icon={Wallet}
          trend={stats.day_change >= 0 ? 'up' : 'down'}
          description="Today's change"
        />

        <StatCard
          title="Total Invested"
          value={formatPrice(stats.total_invested)}
          icon={DollarSign}
          description="Cost basis"
        />

        <StatCard
          title="Unrealized P&L"
          value={formatPrice(stats.total_pnl)}
          changePercent={stats.total_pnl_percent}
          icon={TrendingUp}
          trend={stats.total_pnl >= 0 ? 'up' : 'down'}
          description="Since inception"
        />

        <StatCard
          title="Holdings"
          value={stats.holdings_count}
          icon={BarChart3}
          description="Active positions"
        />
      </div>

      {/* Holdings Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Holdings</span>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">{holdings.length} positions</Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {holdings.map((holding) => {
              const formattedHolding = PortfolioService.formatHolding(holding)
              return (
                <div key={holding.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    {holding.image_url && (
                      <img
                        src={holding.image_url}
                        alt={holding.product_name}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                    )}
                    <div>
                      <h3 className="font-semibold">{holding.product_name}</h3>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Badge
                          variant="secondary"
                          className={PortfolioService.getMetalColor(holding.metal_type)}
                        >
                          {holding.metal_type.toUpperCase()}
                        </Badge>
                        <span>{holding.quantity} units</span>
                        <span>•</span>
                        <span>Avg: {formattedHolding.formatted_average_price}</span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="font-semibold">{formattedHolding.formatted_current_value}</div>
                    <div className={cn("text-sm", formattedHolding.pnl_color)}>
                      {formattedHolding.formatted_profit_loss} ({formattedHolding.formatted_profit_loss_percent})
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Diversification */}
      {stats.diversification && (
        <Card>
          <CardHeader>
            <CardTitle>Asset Allocation</CardTitle>
            <CardDescription>Portfolio diversification by metal type</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(stats.diversification).map(([metal, percentage]) => (
                percentage > 0 && (
                  <div key={metal} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="capitalize font-medium">{metal}</span>
                      <span>{percentage.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={cn(
                          "h-2 rounded-full",
                          metal === 'gold' ? 'bg-yellow-500' :
                          metal === 'silver' ? 'bg-gray-400' :
                          metal === 'platinum' ? 'bg-blue-500' :
                          'bg-purple-500'
                        )}
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                )
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Summary */}
      {(stats.top_performer || stats.worst_performer) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {stats.top_performer && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-green-600" />
                  <span>Top Performer</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-semibold">{stats.top_performer.name}</div>
                  <div className="text-green-600 font-medium">
                    +{stats.top_performer.pnl_percent.toFixed(2)}%
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {stats.worst_performer && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-red-600" />
                  <span>Needs Attention</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-semibold">{stats.worst_performer.name}</div>
                  <div className="text-red-600 font-medium">
                    {stats.worst_performer.pnl_percent.toFixed(2)}%
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}


