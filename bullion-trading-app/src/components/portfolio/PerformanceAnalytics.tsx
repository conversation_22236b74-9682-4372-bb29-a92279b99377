'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Line<PERSON>hart,
  Pie<PERSON>hart,
  Calendar,
  Target,
  Award,
  Activity,
  DollarSign,
  Percent,
  Clock,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'

export interface PerformanceData {
  period: string
  portfolio_value: number
  benchmark_value: number
  return_percent: number
  benchmark_return_percent: number
  date: string
}

export interface PerformanceMetrics {
  total_return: number
  annualized_return: number
  volatility: number
  sharpe_ratio: number
  max_drawdown: number
  best_month: number
  worst_month: number
  win_rate: number
  benchmark_comparison: number
}

interface PerformanceAnalyticsProps {
  performanceData: PerformanceData[]
  metrics: PerformanceMetrics
  className?: string
}

// Mock data for demonstration
const mockPerformanceData: PerformanceData[] = [
  { period: 'Jan 2024', portfolio_value: 2500000, benchmark_value: 2500000, return_percent: 0, benchmark_return_percent: 0, date: '2024-01-01' },
  { period: 'Feb 2024', portfolio_value: 2580000, benchmark_value: 2550000, return_percent: 3.2, benchmark_return_percent: 2.0, date: '2024-02-01' },
  { period: 'Mar 2024', portfolio_value: 2650000, benchmark_value: 2600000, return_percent: 6.0, benchmark_return_percent: 4.0, date: '2024-03-01' },
  { period: 'Apr 2024', portfolio_value: 2720000, benchmark_value: 2640000, return_percent: 8.8, benchmark_return_percent: 5.6, date: '2024-04-01' },
  { period: 'May 2024', portfolio_value: 2800000, benchmark_value: 2680000, return_percent: 12.0, benchmark_return_percent: 7.2, date: '2024-05-01' },
  { period: 'Jun 2024', portfolio_value: 2850000, benchmark_value: 2720000, return_percent: 14.0, benchmark_return_percent: 8.8, date: '2024-06-01' }
]

const mockMetrics: PerformanceMetrics = {
  total_return: 14.0,
  annualized_return: 28.0,
  volatility: 15.2,
  sharpe_ratio: 1.84,
  max_drawdown: -3.2,
  best_month: 4.8,
  worst_month: -2.1,
  win_rate: 83.3,
  benchmark_comparison: 5.2
}

export function PerformanceAnalytics({
  performanceData = mockPerformanceData,
  metrics = mockMetrics,
  className
}: PerformanceAnalyticsProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState('6M')
  const [selectedTab, setSelectedTab] = useState('overview')

  const formatPercent = (percent: number) => {
    return `${percent > 0 ? '+' : ''}${percent.toFixed(2)}%`
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const MetricCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    trend,
    benchmark 
  }: {
    title: string
    value: string | number
    description: string
    icon: React.ComponentType<{ className?: string }>
    trend?: 'up' | 'down' | 'neutral'
    benchmark?: string
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-amber-50 rounded-full">
              <Icon className="h-4 w-4 text-amber-600" />
            </div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
          </div>
          {trend && (
            <div className={cn(
              'p-1 rounded-full',
              trend === 'up' ? 'bg-green-100' : 
              trend === 'down' ? 'bg-red-100' : 'bg-gray-100'
            )}>
              {trend === 'up' && <ArrowUpRight className="h-3 w-3 text-green-600" />}
              {trend === 'down' && <ArrowDownRight className="h-3 w-3 text-red-600" />}
            </div>
          )}
        </div>
        
        <div className="space-y-1">
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          <p className="text-xs text-gray-500">{description}</p>
          {benchmark && (
            <p className="text-xs text-blue-600">vs benchmark: {benchmark}</p>
          )}
        </div>
      </CardContent>
    </Card>
  )

  const PerformanceChart = () => (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Portfolio Performance</CardTitle>
            <CardDescription>Portfolio vs benchmark comparison</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {['1M', '3M', '6M', '1Y', 'ALL'].map((timeframe) => (
              <Button
                key={timeframe}
                variant={selectedTimeframe === timeframe ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTimeframe(timeframe)}
              >
                {timeframe}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <LineChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Performance Chart
            </h3>
            <p className="text-gray-600">
              Interactive performance chart will be displayed here
            </p>
          </div>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center gap-6 mt-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Portfolio</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
            <span className="text-sm text-gray-600">Benchmark</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const ReturnsTable = () => (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Returns</CardTitle>
        <CardDescription>Detailed monthly performance breakdown</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Period</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Portfolio Value</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Portfolio Return</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Benchmark Return</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Outperformance</th>
              </tr>
            </thead>
            <tbody>
              {performanceData.map((data, index) => {
                const outperformance = data.return_percent - data.benchmark_return_percent
                return (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <span className="text-sm font-medium">{data.period}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-sm">{formatPrice(data.portfolio_value)}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={cn(
                        'text-sm font-medium',
                        data.return_percent >= 0 ? 'text-green-600' : 'text-red-600'
                      )}>
                        {formatPercent(data.return_percent)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={cn(
                        'text-sm',
                        data.benchmark_return_percent >= 0 ? 'text-green-600' : 'text-red-600'
                      )}>
                        {formatPercent(data.benchmark_return_percent)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-1">
                        {outperformance >= 0 ? (
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        )}
                        <span className={cn(
                          'text-sm font-medium',
                          outperformance >= 0 ? 'text-green-600' : 'text-red-600'
                        )}>
                          {formatPercent(outperformance)}
                        </span>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )

  const RiskMetrics = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Risk Metrics</CardTitle>
          <CardDescription>Portfolio risk analysis</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Volatility</span>
            <span className="text-sm font-medium">{formatPercent(metrics.volatility)}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Sharpe Ratio</span>
            <span className="text-sm font-medium">{metrics.sharpe_ratio.toFixed(2)}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Max Drawdown</span>
            <span className="text-sm font-medium text-red-600">
              {formatPercent(metrics.max_drawdown)}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Win Rate</span>
            <span className="text-sm font-medium text-green-600">
              {formatPercent(metrics.win_rate)}
            </span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Performance Highlights</CardTitle>
          <CardDescription>Key performance indicators</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Best Month</span>
            <span className="text-sm font-medium text-green-600">
              {formatPercent(metrics.best_month)}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Worst Month</span>
            <span className="text-sm font-medium text-red-600">
              {formatPercent(metrics.worst_month)}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">vs Benchmark</span>
            <span className="text-sm font-medium text-green-600">
              {formatPercent(metrics.benchmark_comparison)}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Annualized Return</span>
            <span className="text-sm font-medium text-green-600">
              {formatPercent(metrics.annualized_return)}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Performance Analytics</h2>
        <p className="text-gray-600">Detailed analysis of your portfolio performance</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Return"
          value={formatPercent(metrics.total_return)}
          description="Since inception"
          icon={TrendingUp}
          trend="up"
          benchmark={formatPercent(metrics.benchmark_comparison)}
        />
        
        <MetricCard
          title="Annualized Return"
          value={formatPercent(metrics.annualized_return)}
          description="Year-over-year"
          icon={Calendar}
          trend="up"
        />
        
        <MetricCard
          title="Sharpe Ratio"
          value={metrics.sharpe_ratio.toFixed(2)}
          description="Risk-adjusted return"
          icon={Target}
          trend="up"
        />
        
        <MetricCard
          title="Win Rate"
          value={formatPercent(metrics.win_rate)}
          description="Positive months"
          icon={Award}
          trend="up"
        />
      </div>

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="returns">Returns</TabsTrigger>
          <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <PerformanceChart />
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">
                      {formatPercent(metrics.total_return)}
                    </p>
                    <p className="text-sm text-gray-600">Total Return</p>
                  </div>
                  
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">
                      {metrics.sharpe_ratio.toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-600">Sharpe Ratio</p>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Outperformance vs Benchmark</span>
                    <span className="text-sm font-medium text-green-600">
                      {formatPercent(metrics.benchmark_comparison)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Maximum Drawdown</span>
                    <span className="text-sm font-medium text-red-600">
                      {formatPercent(metrics.max_drawdown)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Monthly Performance</CardTitle>
                <CardDescription>Recent performance trend</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {performanceData.slice(-3).map((data, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium">{data.period}</p>
                        <p className="text-xs text-gray-600">{formatPrice(data.portfolio_value)}</p>
                      </div>
                      <div className="text-right">
                        <p className={cn(
                          'text-sm font-medium',
                          data.return_percent >= 0 ? 'text-green-600' : 'text-red-600'
                        )}>
                          {formatPercent(data.return_percent)}
                        </p>
                        <p className="text-xs text-gray-600">
                          vs {formatPercent(data.benchmark_return_percent)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="returns">
          <ReturnsTable />
        </TabsContent>

        <TabsContent value="risk">
          <RiskMetrics />
        </TabsContent>
      </Tabs>
    </div>
  )
}
