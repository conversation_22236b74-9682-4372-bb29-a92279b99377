'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Product } from '@/components/products/ProductCard'
import { cn } from '@/lib/utils'
import {
  Heart,
  TrendingUp,
  TrendingDown,
  Plus,
  X,
  Search,
  Bell,
  BellOff,
  ShoppingCart,
  Eye,
  Star,
  AlertCircle,
  RefreshCw,
  Filter
} from 'lucide-react'

export interface WatchlistItem extends Product {
  added_date: string
  price_alert_enabled: boolean
  target_price?: number
  alert_type?: 'above' | 'below'
  notes?: string
}

interface WatchlistProps {
  watchlistItems: WatchlistItem[]
  availableProducts?: Product[]
  onAddToWatchlist?: (product: Product) => void
  onRemoveFromWatchlist?: (productId: string) => void
  onSetPriceAlert?: (productId: string, targetPrice: number, alertType: 'above' | 'below') => void
  onToggleAlert?: (productId: string) => void
  onQuickBuy?: (product: Product) => void
  className?: string
}

// Mock data for demonstration
const mockWatchlistItems: WatchlistItem[] = [
  {
    id: '1',
    name: 'Gold Bar 10g',
    metal_type: 'gold',
    weight: 10,
    weight_unit: 'g',
    purity: 99.9,
    current_price: 65500,
    price_change: 500,
    price_change_percent: 0.77,
    category: 'bars',
    in_stock: true,
    stock_quantity: 25,
    image_url: '/images/gold-bar-10g.jpg',
    description: 'Premium 10g gold bar with 99.9% purity',
    added_date: '2024-06-15',
    price_alert_enabled: true,
    target_price: 66000,
    alert_type: 'above',
    notes: 'Buy when price reaches target'
  },
  {
    id: '2',
    name: 'Silver Coin 1oz',
    metal_type: 'silver',
    weight: 1,
    weight_unit: 'oz',
    purity: 99.9,
    current_price: 2550,
    price_change: -25,
    price_change_percent: -0.97,
    category: 'coins',
    in_stock: true,
    stock_quantity: 100,
    image_url: '/images/silver-coin-1oz.jpg',
    description: '1oz silver coin with premium finish',
    added_date: '2024-06-10',
    price_alert_enabled: false,
    notes: 'Good for diversification'
  }
]

export function Watchlist({
  watchlistItems = mockWatchlistItems,
  availableProducts = [],
  onAddToWatchlist,
  onRemoveFromWatchlist,
  onSetPriceAlert,
  onToggleAlert,
  onQuickBuy,
  className
}: WatchlistProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showAddProduct, setShowAddProduct] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [alertPrice, setAlertPrice] = useState('')
  const [alertType, setAlertType] = useState<'above' | 'below'>('above')
  const [showPriceAlert, setShowPriceAlert] = useState(false)

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getMetalColor = (metal: string) => {
    switch (metal) {
      case 'gold': return 'bg-yellow-100 text-yellow-800'
      case 'silver': return 'bg-gray-100 text-gray-800'
      case 'platinum': return 'bg-blue-100 text-blue-800'
      case 'palladium': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriceChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600'
    if (change < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />
    if (change < 0) return <TrendingDown className="h-4 w-4" />
    return null
  }

  const handleSetPriceAlert = (item: WatchlistItem) => {
    setSelectedProduct(item)
    setAlertPrice(item.target_price?.toString() || '')
    setAlertType(item.alert_type || 'above')
    setShowPriceAlert(true)
  }

  const handleSavePriceAlert = () => {
    if (selectedProduct && alertPrice) {
      onSetPriceAlert?.(selectedProduct.id, parseFloat(alertPrice), alertType)
      setShowPriceAlert(false)
      setSelectedProduct(null)
      setAlertPrice('')
    }
  }

  const filteredItems = watchlistItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.metal_type.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Watchlist</h2>
          <p className="text-gray-600">Track your favorite precious metals</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => setShowAddProduct(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search watchlist..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Button variant="outline" size="sm">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Watchlist Items */}
      {filteredItems.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Heart className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No matching items' : 'Your watchlist is empty'}
            </h3>
            <p className="text-gray-600 text-center mb-4">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Add products to your watchlist to track their prices and set alerts'
              }
            </p>
            {!searchQuery && (
              <Button onClick={() => setShowAddProduct(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Product
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredItems.map((item) => (
            <Card key={item.id} className="hover:shadow-md transition-shadow duration-200">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    {/* Product Image */}
                    <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Star className="h-6 w-6 text-gray-400" />
                    </div>
                    
                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium text-gray-900 truncate">{item.name}</h3>
                        <Badge variant="secondary" className={getMetalColor(item.metal_type)}>
                          {item.metal_type}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                        <span>{item.weight}{item.weight_unit}</span>
                        <span>{item.purity}% purity</span>
                        <span>Added {formatDate(item.added_date)}</span>
                      </div>
                      
                      {item.notes && (
                        <p className="text-sm text-gray-600 mb-2">{item.notes}</p>
                      )}
                      
                      {/* Price Alert Info */}
                      {item.price_alert_enabled && item.target_price && (
                        <div className="flex items-center gap-2 text-sm">
                          <Bell className="h-3 w-3 text-blue-600" />
                          <span className="text-blue-600">
                            Alert when price goes {item.alert_type} {formatPrice(item.target_price)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Price and Actions */}
                  <div className="text-right flex-shrink-0 ml-4">
                    <div className="mb-3">
                      <p className="text-xl font-bold text-gray-900">
                        {formatPrice(item.current_price)}
                      </p>
                      <div className={cn('flex items-center justify-end text-sm', getPriceChangeColor(item.price_change))}>
                        {getPriceChangeIcon(item.price_change)}
                        <span className="ml-1">
                          {item.price_change > 0 ? '+' : ''}{formatPrice(item.price_change)} 
                          ({item.price_change_percent > 0 ? '+' : ''}{item.price_change_percent.toFixed(2)}%)
                        </span>
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetPriceAlert(item)}
                      >
                        {item.price_alert_enabled ? (
                          <Bell className="h-4 w-4" />
                        ) : (
                          <BellOff className="h-4 w-4" />
                        )}
                      </Button>
                      
                      {onQuickBuy && (
                        <Button
                          size="sm"
                          onClick={() => onQuickBuy(item)}
                          disabled={!item.in_stock}
                        >
                          <ShoppingCart className="h-4 w-4 mr-1" />
                          Buy
                        </Button>
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onRemoveFromWatchlist?.(item.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Price Alert Modal */}
      {showPriceAlert && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Set Price Alert</CardTitle>
              <CardDescription>
                Get notified when {selectedProduct.name} reaches your target price
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Alert Type
                </label>
                <div className="flex gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="above"
                      checked={alertType === 'above'}
                      onChange={(e) => setAlertType(e.target.value as 'above' | 'below')}
                      className="mr-2"
                    />
                    <span className="text-sm">Price goes above</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="below"
                      checked={alertType === 'below'}
                      onChange={(e) => setAlertType(e.target.value as 'above' | 'below')}
                      className="mr-2"
                    />
                    <span className="text-sm">Price goes below</span>
                  </label>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Price
                </label>
                <Input
                  type="number"
                  value={alertPrice}
                  onChange={(e) => setAlertPrice(e.target.value)}
                  placeholder="Enter target price"
                  min="0"
                  step="0.01"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Current price: {formatPrice(selectedProduct.current_price)}
                </p>
              </div>
              
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  You'll receive notifications via WhatsApp when the price condition is met.
                </AlertDescription>
              </Alert>
              
              <div className="flex gap-3 pt-4">
                <Button onClick={handleSavePriceAlert} className="flex-1">
                  Set Alert
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowPriceAlert(false)
                    setSelectedProduct(null)
                    setAlertPrice('')
                  }}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Add Product Modal */}
      {showAddProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Add to Watchlist</CardTitle>
              <CardDescription>
                Select products to add to your watchlist
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search products..."
                    className="pl-10"
                  />
                </div>
                
                <div className="text-center py-8">
                  <Plus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Product Selection
                  </h3>
                  <p className="text-gray-600">
                    Product selection interface will be implemented here
                  </p>
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddProduct(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
