'use client'

import { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, Minus, Wifi, WifiOff } from 'lucide-react'
import { MetalRateData } from '@/lib/api/metal-rates'
import { cn } from '@/lib/utils'

interface PriceCardProps {
  data: MetalRateData
  isConnected: boolean
  className?: string
}

export function PriceCard({ data, isConnected, className }: PriceCardProps) {
  const [previousPrice, setPreviousPrice] = useState<number | null>(null)
  const [priceDirection, setPriceDirection] = useState<'up' | 'down' | 'same'>('same')
  const [isFlashing, setIsFlashing] = useState(false)

  useEffect(() => {
    if (previousPrice !== null && data.price !== previousPrice) {
      if (data.price > previousPrice) {
        setPriceDirection('up')
      } else if (data.price < previousPrice) {
        setPriceDirection('down')
      } else {
        setPriceDirection('same')
      }
      
      // Flash effect for price changes
      setIsFlashing(true)
      const timer = setTimeout(() => setIsFlashing(false), 1000)
      return () => clearTimeout(timer)
    }
    setPreviousPrice(data.price)
  }, [data.price, previousPrice])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: data.currency || 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price)
  }

  const formatChange = (change: number) => {
    const sign = change >= 0 ? '+' : ''
    return `${sign}${change.toFixed(2)}`
  }

  const formatPercentage = (percentage: number) => {
    const sign = percentage >= 0 ? '+' : ''
    return `${sign}${percentage.toFixed(2)}%`
  }

  const getTrendIcon = () => {
    switch (priceDirection) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Minus className="h-4 w-4 text-gray-400" />
    }
  }

  const getChangeColor = (change?: number) => {
    if (!change) return 'text-gray-500'
    return change >= 0 ? 'text-green-600' : 'text-red-600'
  }

  const getPriceChangeColor = () => {
    switch (priceDirection) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-gray-900'
    }
  }

  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-lg border bg-white p-6 shadow-sm transition-all duration-300',
        isFlashing && priceDirection === 'up' && 'bg-green-50 border-green-200',
        isFlashing && priceDirection === 'down' && 'bg-red-50 border-red-200',
        !isConnected && 'opacity-75',
        className
      )}
    >
      {/* Connection Status Indicator */}
      <div className="absolute top-2 right-2">
        {isConnected ? (
          <Wifi className="h-4 w-4 text-green-500" />
        ) : (
          <WifiOff className="h-4 w-4 text-red-500" />
        )}
      </div>

      {/* Metal Name and Symbol */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{data.name}</h3>
        <p className="text-sm text-gray-500">{data.symbol}</p>
      </div>

      {/* Current Price */}
      <div className="mb-4">
        <div className="flex items-center gap-2">
          {getTrendIcon()}
          <span
            className={cn(
              'text-2xl font-bold transition-colors duration-300',
              getPriceChangeColor()
            )}
          >
            {formatPrice(data.price)}
          </span>
        </div>
        <p className="text-xs text-gray-400 mt-1">
          per unit • {data.currency || 'INR'}
        </p>
      </div>

      {/* Price Changes */}
      {(data.change_24h !== undefined || data.change_percentage_24h !== undefined) && (
        <div className="space-y-1">
          {data.change_24h !== undefined && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">24h Change:</span>
              <span className={cn('text-sm font-medium', getChangeColor(data.change_24h))}>
                {formatChange(data.change_24h)}
              </span>
            </div>
          )}
          {data.change_percentage_24h !== undefined && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">24h %:</span>
              <span className={cn('text-sm font-medium', getChangeColor(data.change_percentage_24h))}>
                {formatPercentage(data.change_percentage_24h)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Last Update */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-400">
          Last updated: {new Date(data.timestamp).toLocaleTimeString()}
        </p>
      </div>

      {/* Flash overlay for price changes */}
      {isFlashing && (
        <div
          className={cn(
            'absolute inset-0 pointer-events-none transition-opacity duration-1000',
            priceDirection === 'up' && 'bg-green-100 opacity-50',
            priceDirection === 'down' && 'bg-red-100 opacity-50'
          )}
        />
      )}
    </div>
  )
}

// Grid component for displaying multiple price cards
interface PriceGridProps {
  prices: Record<string, MetalRateData>
  isConnected: boolean
  className?: string
}

export function PriceGrid({ prices, isConnected, className }: PriceGridProps) {
  const priceArray = Object.values(prices)

  if (priceArray.length === 0) {
    return (
      <div className={cn('text-center py-8', className)}>
        <p className="text-gray-500">No price data available</p>
      </div>
    )
  }

  return (
    <div className={cn('grid gap-6 md:grid-cols-2 lg:grid-cols-4', className)}>
      {priceArray.map((price) => (
        <PriceCard
          key={price.symbol}
          data={price}
          isConnected={isConnected}
        />
      ))}
    </div>
  )
}
