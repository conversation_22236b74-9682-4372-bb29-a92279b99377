'use client'

import { useState } from 'react'
import { Refresh<PERSON>w, AlertCircle, Wifi, WifiOff, Clock } from 'lucide-react'
import { usePrices } from '@/hooks/usePrices'
import { PriceGrid } from './PriceCard'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'

interface PriceDashboardProps {
  className?: string
}

export function PriceDashboard({ className }: PriceDashboardProps) {
  const { prices, isConnected, isLoading, error, lastUpdate, reconnect } = usePrices()
  const [isReconnecting, setIsReconnecting] = useState(false)

  const handleReconnect = async () => {
    setIsReconnecting(true)
    reconnect()
    // Reset reconnecting state after a delay
    setTimeout(() => setIsReconnecting(false), 2000)
  }

  const formatLastUpdate = (date: Date | null) => {
    if (!date) return 'Never'
    
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffSeconds = Math.floor(diffMs / 1000)
    const diffMinutes = Math.floor(diffSeconds / 60)
    
    if (diffSeconds < 60) {
      return `${diffSeconds}s ago`
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`
    } else {
      return date.toLocaleTimeString()
    }
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Live Metal Prices</h2>
          <p className="text-gray-600">Real-time bullion market rates</p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            {isConnected ? (
              <>
                <Wifi className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600 font-medium">Connected</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-600 font-medium">Disconnected</span>
              </>
            )}
          </div>

          {/* Last Update */}
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Clock className="h-4 w-4" />
            <span>Updated {formatLastUpdate(lastUpdate)}</span>
          </div>

          {/* Reconnect Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleReconnect}
            disabled={isReconnecting || isConnected}
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn('h-4 w-4', isReconnecting && 'animate-spin')} />
            {isReconnecting ? 'Reconnecting...' : 'Reconnect'}
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReconnect}
              className="ml-4"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && Object.keys(prices).length === 0 && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-amber-600" />
            <p className="text-gray-600">Loading live prices...</p>
          </div>
        </div>
      )}

      {/* Price Grid */}
      {Object.keys(prices).length > 0 && (
        <PriceGrid
          prices={prices}
          isConnected={isConnected}
        />
      )}

      {/* No Data State */}
      {!isLoading && Object.keys(prices).length === 0 && !error && (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Price Data</h3>
          <p className="text-gray-600 mb-4">
            Unable to load price information at the moment.
          </p>
          <Button onClick={handleReconnect} variant="outline">
            Try Again
          </Button>
        </div>
      )}

      {/* Market Status Footer */}
      <div className="border-t pt-4">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-4">
            <span>Market Status: {isConnected ? 'Live' : 'Offline'}</span>
            <span>•</span>
            <span>Prices in INR per unit</span>
          </div>
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-2 h-2 rounded-full',
              isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
            )} />
            <span>{isConnected ? 'Real-time updates' : 'Updates paused'}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

// Compact version for smaller spaces
export function CompactPriceDashboard({ className }: PriceDashboardProps) {
  const { prices, isConnected, isLoading, error } = usePrices()

  if (isLoading && Object.keys(prices).length === 0) {
    return (
      <div className={cn('flex items-center gap-2 text-sm text-gray-500', className)}>
        <RefreshCw className="h-4 w-4 animate-spin" />
        <span>Loading prices...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn('flex items-center gap-2 text-sm text-red-600', className)}>
        <AlertCircle className="h-4 w-4" />
        <span>Price feed error</span>
      </div>
    )
  }

  const mainMetals = ['GOLD', 'SILVER', 'PLATINUM', 'PALLADIUM']
  const displayPrices = mainMetals
    .map(symbol => prices[symbol])
    .filter(Boolean)
    .slice(0, 4)

  return (
    <div className={cn('flex items-center gap-4', className)}>
      {displayPrices.map((price) => (
        <div key={price.symbol} className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">{price.symbol}:</span>
          <span className="text-sm font-bold text-gray-900">
            ₹{price.price.toLocaleString()}
          </span>
          {price.change_percentage_24h !== undefined && (
            <span className={cn(
              'text-xs',
              price.change_percentage_24h >= 0 ? 'text-green-600' : 'text-red-600'
            )}>
              ({price.change_percentage_24h >= 0 ? '+' : ''}{price.change_percentage_24h.toFixed(2)}%)
            </span>
          )}
        </div>
      ))}
      <div className="flex items-center gap-1">
        {isConnected ? (
          <Wifi className="h-3 w-3 text-green-500" />
        ) : (
          <WifiOff className="h-3 w-3 text-red-500" />
        )}
      </div>
    </div>
  )
}
