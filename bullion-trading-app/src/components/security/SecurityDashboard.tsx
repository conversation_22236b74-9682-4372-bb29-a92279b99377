'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Activity,
  Eye,
  Lock,
  Users,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react'

interface SecurityMetrics {
  timestamp: number
  failedLogins: number
  suspiciousActivities: number
  rateLimitExceeded: number
  maliciousRequests: number
  unauthorizedAccess: number
  systemErrors: number
  activeUsers: number
  totalRequests: number
  averageResponseTime: number
}

interface SecurityAlert {
  id: string
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  description: string
  timestamp: number
  resolved: boolean
}

interface SecurityDashboardData {
  currentMetrics: SecurityMetrics | null
  activeAlerts: SecurityAlert[]
  metricsHistory: SecurityMetrics[]
  alertsSummary: Record<string, number>
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

interface SecurityTestResult {
  testName: string
  passed: boolean
  message: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

interface SecurityTestData {
  summary: {
    total: number
    passed: number
    failed: number
    critical: number
    high: number
    medium: number
    low: number
  }
  criticalIssues: SecurityTestResult[]
  recommendations: string[]
}

export default function SecurityDashboard() {
  const [dashboardData, setDashboardData] = useState<SecurityDashboardData | null>(null)
  const [testData, setTestData] = useState<SecurityTestData | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchSecurityData()
    const interval = setInterval(fetchSecurityData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const fetchSecurityData = async () => {
    try {
      const [dashboardResponse, testResponse] = await Promise.all([
        fetch('/api/security/test?action=dashboard'),
        fetch('/api/security/test?action=test')
      ])

      if (dashboardResponse.ok) {
        const dashboardResult = await dashboardResponse.json()
        setDashboardData(dashboardResult.data)
      }

      if (testResponse.ok) {
        const testResult = await testResponse.json()
        setTestData(testResult.data)
      }
    } catch (error) {
      console.error('Failed to fetch security data:', error)
    } finally {
      setLoading(false)
    }
  }

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch('/api/security/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'resolve_alert', alertId })
      })

      if (response.ok) {
        fetchSecurityData() // Refresh data
      }
    } catch (error) {
      console.error('Failed to resolve alert:', error)
    }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'CRITICAL': return 'destructive'
      case 'HIGH': return 'destructive'
      case 'MEDIUM': return 'default'
      case 'LOW': return 'secondary'
      default: return 'secondary'
    }
  }

  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'CRITICAL': return <XCircle className="h-4 w-4" />
      case 'HIGH': return <AlertTriangle className="h-4 w-4" />
      case 'MEDIUM': return <Eye className="h-4 w-4" />
      case 'LOW': return <CheckCircle className="h-4 w-4" />
      default: return <Shield className="h-4 w-4" />
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-red-500" />
      case 'decreasing': return <TrendingDown className="h-4 w-4 text-green-500" />
      default: return <Minus className="h-4 w-4 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Security Dashboard</h1>
          <p className="text-muted-foreground">Monitor and manage application security</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={getRiskLevelColor(dashboardData?.riskLevel || 'LOW')}>
            {getRiskLevelIcon(dashboardData?.riskLevel || 'LOW')}
            <span className="ml-1">{dashboardData?.riskLevel || 'LOW'} Risk</span>
          </Badge>
          <Button onClick={fetchSecurityData} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="tests">Security Tests</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Security Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.activeAlerts.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.activeAlerts.filter(a => a.severity === 'CRITICAL').length || 0} critical
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed Logins</CardTitle>
                <Lock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.currentMetrics?.failedLogins || 0}
                </div>
                <p className="text-xs text-muted-foreground">Last minute</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Blocked Requests</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(dashboardData?.currentMetrics?.maliciousRequests || 0) + 
                   (dashboardData?.currentMetrics?.rateLimitExceeded || 0)}
                </div>
                <p className="text-xs text-muted-foreground">Last minute</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">System Health</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {testData ? Math.round((testData.summary.passed / testData.summary.total) * 100) : 0}%
                </div>
                <p className="text-xs text-muted-foreground">Tests passing</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Alerts */}
          {dashboardData?.activeAlerts && dashboardData.activeAlerts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Active Security Alerts</CardTitle>
                <CardDescription>Alerts requiring immediate attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {dashboardData.activeAlerts.slice(0, 5).map((alert) => (
                    <Alert key={alert.id} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant={getRiskLevelColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                          <AlertTitle className="text-sm">{alert.title}</AlertTitle>
                        </div>
                        <AlertDescription className="text-xs mt-1">
                          {alert.description}
                        </AlertDescription>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => resolveAlert(alert.id)}
                      >
                        Resolve
                      </Button>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security Recommendations */}
          {testData?.recommendations && testData.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Security Recommendations</CardTitle>
                <CardDescription>Actions to improve security posture</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {testData.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="text-sm">{recommendation}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Security Alerts</CardTitle>
              <CardDescription>All security alerts and their status</CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardData?.activeAlerts && dashboardData.activeAlerts.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.activeAlerts.map((alert) => (
                    <div key={alert.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant={getRiskLevelColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                          <span className="font-medium">{alert.title}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">
                            {new Date(alert.timestamp).toLocaleString()}
                          </span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => resolveAlert(alert.id)}
                          >
                            Resolve
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">{alert.description}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <p className="text-lg font-medium">No Active Alerts</p>
                  <p className="text-muted-foreground">Your system is secure</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tests">
          <Card>
            <CardHeader>
              <CardTitle>Security Test Results</CardTitle>
              <CardDescription>Automated security testing results</CardDescription>
            </CardHeader>
            <CardContent>
              {testData ? (
                <div className="space-y-6">
                  {/* Test Summary */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{testData.summary.passed}</div>
                      <div className="text-sm text-muted-foreground">Passed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{testData.summary.failed}</div>
                      <div className="text-sm text-muted-foreground">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-800">{testData.summary.critical}</div>
                      <div className="text-sm text-muted-foreground">Critical</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{testData.summary.high}</div>
                      <div className="text-sm text-muted-foreground">High</div>
                    </div>
                  </div>

                  {/* Critical Issues */}
                  {testData.criticalIssues.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Critical Issues</h3>
                      <div className="space-y-2">
                        {testData.criticalIssues.map((issue, index) => (
                          <Alert key={index} variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertTitle>{issue.testName}</AlertTitle>
                            <AlertDescription>{issue.message}</AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p>Running security tests...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics">
          <Card>
            <CardHeader>
              <CardTitle>Security Metrics</CardTitle>
              <CardDescription>Real-time security monitoring data</CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardData?.currentMetrics ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Failed Logins</span>
                      {getTrendIcon('stable')}
                    </div>
                    <div className="text-2xl font-bold">{dashboardData.currentMetrics.failedLogins}</div>
                  </div>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Suspicious Activities</span>
                      {getTrendIcon('stable')}
                    </div>
                    <div className="text-2xl font-bold">{dashboardData.currentMetrics.suspiciousActivities}</div>
                  </div>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Rate Limit Exceeded</span>
                      {getTrendIcon('stable')}
                    </div>
                    <div className="text-2xl font-bold">{dashboardData.currentMetrics.rateLimitExceeded}</div>
                  </div>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Malicious Requests</span>
                      {getTrendIcon('stable')}
                    </div>
                    <div className="text-2xl font-bold">{dashboardData.currentMetrics.maliciousRequests}</div>
                  </div>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Unauthorized Access</span>
                      {getTrendIcon('stable')}
                    </div>
                    <div className="text-2xl font-bold">{dashboardData.currentMetrics.unauthorizedAccess}</div>
                  </div>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">System Errors</span>
                      {getTrendIcon('stable')}
                    </div>
                    <div className="text-2xl font-bold">{dashboardData.currentMetrics.systemErrors}</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p>No metrics data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
