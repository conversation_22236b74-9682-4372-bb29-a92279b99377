'use client'

import { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ProductGrid, Product } from './ProductCard'
import { FilterSidebar } from '@/components/layout/Sidebar'
import { cn } from '@/lib/utils'
import {
  Search,
  Filter,
  SlidersHorizontal,
  Grid3X3,
  List,
  ChevronDown,
  Star,
  TrendingUp,
  DollarSign,
  X
} from 'lucide-react'

interface ProductCatalogProps {
  products: Product[]
  title?: string
  description?: string
  showFilters?: boolean
  showSearch?: boolean
  showSorting?: boolean
  defaultView?: 'grid' | 'list'
  onAddToCart?: (product: Product) => void
  onAddToWishlist?: (product: Product) => void
  className?: string
}

type SortOption = 'name' | 'price-low' | 'price-high' | 'rating' | 'newest' | 'popular'

interface FilterState {
  metalType: string
  priceRange: string
  purity: string
  weight: string
  inStock: boolean
  featured: boolean
}

const sortOptions: { value: SortOption; label: string }[] = [
  { value: 'name', label: 'Name A-Z' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'newest', label: 'Newest First' },
  { value: 'popular', label: 'Most Popular' }
]

export function ProductCatalog({
  products,
  title = 'Product Catalog',
  description,
  showFilters = true,
  showSearch = true,
  showSorting = true,
  defaultView = 'grid',
  onAddToCart,
  onAddToWishlist,
  className
}: ProductCatalogProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<SortOption>('name')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(defaultView)
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [filters, setFilters] = useState<FilterState>({
    metalType: '',
    priceRange: '',
    purity: '',
    weight: '',
    inStock: false,
    featured: false
  })

  // Filter and sort products
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.metal_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply filters
    if (filters.metalType) {
      filtered = filtered.filter(product => product.metal_type === filters.metalType)
    }

    if (filters.priceRange) {
      const [min, max] = filters.priceRange.split('-').map(Number)
      filtered = filtered.filter(product => {
        if (max) {
          return product.current_price >= min && product.current_price <= max
        } else {
          return product.current_price >= min
        }
      })
    }

    if (filters.purity) {
      filtered = filtered.filter(product => product.purity >= Number(filters.purity))
    }

    if (filters.inStock) {
      filtered = filtered.filter(product => product.in_stock)
    }

    if (filters.featured) {
      filtered = filtered.filter(product => product.is_featured)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'price-low':
          return a.current_price - b.current_price
        case 'price-high':
          return b.current_price - a.current_price
        case 'rating':
          return (b.rating || 0) - (a.rating || 0)
        case 'newest':
          return a.is_new ? -1 : b.is_new ? 1 : 0
        case 'popular':
          return (b.review_count || 0) - (a.review_count || 0)
        default:
          return 0
      }
    })

    return filtered
  }, [products, searchQuery, filters, sortBy])

  const handleFilterChange = (newFilters: Record<string, any>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const clearFilters = () => {
    setFilters({
      metalType: '',
      priceRange: '',
      purity: '',
      weight: '',
      inStock: false,
      featured: false
    })
    setSearchQuery('')
  }

  const activeFilterCount = Object.values(filters).filter(value => 
    typeof value === 'boolean' ? value : value !== ''
  ).length + (searchQuery ? 1 : 0)

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
          {description && (
            <p className="text-gray-600 mt-1">{description}</p>
          )}
        </div>

        {/* View Toggle */}
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Search and Controls */}
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        {showSearch && (
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        )}

        {/* Sort */}
        {showSorting && (
          <div className="relative">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortOption)}
              className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        )}

        {/* Mobile Filter Toggle */}
        {showFilters && (
          <Button
            variant="outline"
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            className="lg:hidden"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-gray-600">Active filters:</span>
          {searchQuery && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: {searchQuery}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setSearchQuery('')}
              />
            </Badge>
          )}
          {filters.metalType && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Metal: {filters.metalType}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setFilters(prev => ({ ...prev, metalType: '' }))}
              />
            </Badge>
          )}
          {filters.featured && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Featured
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setFilters(prev => ({ ...prev, featured: false }))}
              />
            </Badge>
          )}
          {filters.inStock && (
            <Badge variant="secondary" className="flex items-center gap-1">
              In Stock
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setFilters(prev => ({ ...prev, inStock: false }))}
              />
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-xs"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {filteredAndSortedProducts.length} of {products.length} products
        </p>
      </div>

      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Desktop Filters Sidebar */}
        {showFilters && (
          <div className="hidden lg:block w-80 flex-shrink-0">
            <FilterSidebar onFilterChange={handleFilterChange} />
          </div>
        )}

        {/* Mobile Filters */}
        {showFilters && showMobileFilters && (
          <div className="lg:hidden">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Filters</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowMobileFilters(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <FilterSidebar onFilterChange={handleFilterChange} />
              </CardContent>
            </Card>
          </div>
        )}

        {/* Products Grid/List */}
        <div className="flex-1">
          {filteredAndSortedProducts.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Search className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No products found
                </h3>
                <p className="text-gray-600 text-center mb-4">
                  Try adjusting your search or filter criteria
                </p>
                <Button onClick={clearFilters}>
                  Clear filters
                </Button>
              </CardContent>
            </Card>
          ) : (
            <ProductGrid
              products={filteredAndSortedProducts}
              variant={viewMode === 'list' ? 'compact' : 'default'}
              columns={viewMode === 'list' ? 1 : 3}
              onAddToCart={onAddToCart}
              onAddToWishlist={onAddToWishlist}
            />
          )}
        </div>
      </div>
    </div>
  )
}
