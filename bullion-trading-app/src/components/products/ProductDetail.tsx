'use client'

import { useState } from 'react'
import Image from 'next/image'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Product } from './ProductCard'
import { cn } from '@/lib/utils'
import {
  Star,
  Heart,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Minus,
  Share2,
  Award,
  Shield,
  Truck,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Plus,
  Minus as MinusIcon,
  Info
} from 'lucide-react'

interface ProductDetailProps {
  product: Product
  onAddToCart?: (product: Product, quantity: number) => void
  onAddToWishlist?: (product: Product) => void
  className?: string
}

export function ProductDetail({
  product,
  onAddToCart,
  onAddToWishlist,
  className
}: ProductDetailProps) {
  const [quantity, setQuantity] = useState(1)
  const [selectedImage, setSelectedImage] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleAddToCart = () => {
    onAddToCart?.(product, quantity)
  }

  const handleToggleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    onAddToWishlist?.(product)
  }

  const handleQuantityChange = (change: number) => {
    const newQuantity = Math.max(1, quantity + change)
    if (product.stock_quantity) {
      setQuantity(Math.min(newQuantity, product.stock_quantity))
    } else {
      setQuantity(newQuantity)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatWeight = (weight: number, unit: string) => {
    return `${weight}${unit}`
  }

  const getPriceChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600'
    if (change < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />
    if (change < 0) return <TrendingDown className="h-4 w-4" />
    return <Minus className="h-4 w-4" />
  }

  const getMetalColor = (metal: string) => {
    switch (metal) {
      case 'gold': return 'bg-yellow-100 text-yellow-800'
      case 'silver': return 'bg-gray-100 text-gray-800'
      case 'platinum': return 'bg-blue-100 text-blue-800'
      case 'palladium': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const totalPrice = product.current_price * quantity

  // Mock additional images for demo
  const images = [
    product.image_url,
    product.image_url,
    product.image_url
  ].filter(Boolean)

  return (
    <div className={cn('space-y-8', className)}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Gallery */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
            {product.image_url && !imageError ? (
              <Image
                src={images[selectedImage] || product.image_url}
                alt={product.name}
                fill
                className="object-cover"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                <Award className="h-24 w-24 text-gray-400" />
              </div>
            )}

            {/* Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
              {product.is_new && (
                <Badge className="bg-green-500 hover:bg-green-600">New</Badge>
              )}
              {product.is_featured && (
                <Badge className="bg-amber-500 hover:bg-amber-600">Featured</Badge>
              )}
              {product.discount_percent && (
                <Badge variant="destructive">-{product.discount_percent}%</Badge>
              )}
            </div>

            {/* Share Button */}
            <div className="absolute top-4 right-4">
              <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Thumbnail Images */}
          {images.length > 1 && (
            <div className="flex gap-2">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={cn(
                    'relative w-20 h-20 rounded-md overflow-hidden border-2 transition-colors',
                    selectedImage === index ? 'border-amber-500' : 'border-gray-200'
                  )}
                >
                  {image ? (
                    <Image
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <Award className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {product.name}
                </h1>
                <p className="text-gray-600">
                  {product.description}
                </p>
              </div>
              <Badge variant="secondary" className={getMetalColor(product.metal_type)}>
                {product.metal_type}
              </Badge>
            </div>

            {/* Rating */}
            {product.rating && (
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        'h-5 w-5',
                        i < Math.floor(product.rating!) 
                          ? 'text-yellow-400 fill-yellow-400' 
                          : 'text-gray-300'
                      )}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  {product.rating.toFixed(1)} ({product.review_count || 0} reviews)
                </span>
              </div>
            )}
          </div>

          {/* Price */}
          <div className="space-y-2">
            <div className="flex items-baseline gap-4">
              <span className="text-4xl font-bold text-gray-900">
                {formatPrice(product.current_price)}
              </span>
              <div className={cn('flex items-center', getPriceChangeColor(product.price_change))}>
                {getPriceChangeIcon(product.price_change)}
                <span className="ml-1 text-lg font-medium">
                  {product.price_change_percent > 0 ? '+' : ''}{product.price_change_percent.toFixed(2)}%
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-600">
              Price per {formatWeight(product.weight, product.weight_unit)}
            </p>
          </div>

          {/* Product Details */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">Specifications</h3>
              <div className="space-y-1 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Weight:</span>
                  <span>{formatWeight(product.weight, product.weight_unit)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Purity:</span>
                  <span>{product.purity}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Category:</span>
                  <span>{product.category}</span>
                </div>
                {product.brand && (
                  <div className="flex justify-between">
                    <span>Brand:</span>
                    <span>{product.brand}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">Availability</h3>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  {product.in_stock ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-green-600">In Stock</span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-red-600">Out of Stock</span>
                    </>
                  )}
                </div>
                {product.stock_quantity && (
                  <p className="text-gray-600">
                    {product.stock_quantity} units available
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Features */}
          {product.features && product.features.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">Features</h3>
              <div className="flex flex-wrap gap-2">
                {product.features.map((feature, index) => (
                  <Badge key={index} variant="outline">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Quantity and Actions */}
          <div className="space-y-4">
            {/* Quantity Selector */}
            <div className="flex items-center gap-4">
              <label className="font-medium text-gray-900">Quantity:</label>
              <div className="flex items-center border border-gray-300 rounded-md">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleQuantityChange(-1)}
                  disabled={quantity <= 1}
                  className="px-3"
                >
                  <MinusIcon className="h-4 w-4" />
                </Button>
                <Input
                  type="number"
                  value={quantity}
                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  className="w-20 text-center border-0 focus:ring-0"
                  min="1"
                  max={product.stock_quantity}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleQuantityChange(1)}
                  disabled={product.stock_quantity ? quantity >= product.stock_quantity : false}
                  className="px-3"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Total Price */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <span className="font-medium text-gray-900">Total:</span>
              <span className="text-2xl font-bold text-gray-900">
                {formatPrice(totalPrice)}
              </span>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                onClick={handleAddToCart}
                disabled={!product.in_stock}
                className="flex-1 h-12"
                size="lg"
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                {product.in_stock ? 'Add to Cart' : 'Out of Stock'}
              </Button>
              <Button
                variant="outline"
                onClick={handleToggleWishlist}
                className="h-12 px-4"
                size="lg"
              >
                <Heart className={cn('h-5 w-5', isWishlisted ? 'fill-red-500 text-red-500' : '')} />
              </Button>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-3 gap-4 pt-6 border-t">
            <div className="flex flex-col items-center text-center">
              <Shield className="h-8 w-8 text-amber-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Certified</span>
              <span className="text-xs text-gray-600">100% Authentic</span>
            </div>
            <div className="flex flex-col items-center text-center">
              <Truck className="h-8 w-8 text-amber-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Free Shipping</span>
              <span className="text-xs text-gray-600">On all orders</span>
            </div>
            <div className="flex flex-col items-center text-center">
              <RefreshCw className="h-8 w-8 text-amber-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Easy Returns</span>
              <span className="text-xs text-gray-600">30-day policy</span>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle>Product Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 leading-relaxed">
              {product.description}
            </p>
            {/* Add more detailed description here */}
            <div className="mt-4 space-y-2">
              <h4 className="font-medium text-gray-900">Key Benefits:</h4>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>Investment grade precious metal</li>
                <li>Certified purity and authenticity</li>
                <li>Secure storage and delivery options</li>
                <li>Competitive market pricing</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Specifications */}
        <Card>
          <CardHeader>
            <CardTitle>Detailed Specifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-gray-600">Metal Type:</span>
                <span className="font-medium">{product.metal_type}</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-gray-600">Weight:</span>
                <span className="font-medium">{formatWeight(product.weight, product.weight_unit)}</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-gray-600">Purity:</span>
                <span className="font-medium">{product.purity}%</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-gray-600">Category:</span>
                <span className="font-medium">{product.category}</span>
              </div>
              {product.brand && (
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Brand:</span>
                  <span className="font-medium">{product.brand}</span>
                </div>
              )}
              <div className="flex justify-between py-2">
                <span className="text-gray-600">SKU:</span>
                <span className="font-medium">{product.id}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
