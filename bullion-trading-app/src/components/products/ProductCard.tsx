'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import {
  Star,
  Heart,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Minus,
  Eye,
  Award,
  Shield,
  Truck
} from 'lucide-react'

export interface Product {
  id: string
  name: string
  description: string
  metal_type: 'gold' | 'silver' | 'platinum' | 'palladium'
  weight: number
  weight_unit: 'g' | 'oz' | 'kg'
  purity: number
  current_price: number
  price_change: number
  price_change_percent: number
  image_url?: string
  category: string
  brand?: string
  in_stock: boolean
  stock_quantity?: number
  rating?: number
  review_count?: number
  features?: string[]
  is_featured?: boolean
  is_new?: boolean
  discount_percent?: number
}

interface ProductCardProps {
  product: Product
  variant?: 'default' | 'compact' | 'featured'
  showActions?: boolean
  onAddToCart?: (product: Product) => void
  onAddToWishlist?: (product: Product) => void
  className?: string
}

export function ProductCard({
  product,
  variant = 'default',
  showActions = true,
  onAddToCart,
  onAddToWishlist,
  className
}: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleAddToCart = () => {
    onAddToCart?.(product)
  }

  const handleToggleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    onAddToWishlist?.(product)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatWeight = (weight: number, unit: string) => {
    return `${weight}${unit}`
  }

  const getPriceChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600'
    if (change < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getPriceChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-3 w-3" />
    if (change < 0) return <TrendingDown className="h-3 w-3" />
    return <Minus className="h-3 w-3" />
  }

  const getMetalColor = (metal: string) => {
    switch (metal) {
      case 'gold': return 'bg-yellow-100 text-yellow-800'
      case 'silver': return 'bg-gray-100 text-gray-800'
      case 'platinum': return 'bg-blue-100 text-blue-800'
      case 'palladium': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (variant === 'compact') {
    return (
      <Card className={cn('hover:shadow-md transition-shadow duration-200', className)}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="relative w-16 h-16 flex-shrink-0">
              {product.image_url && !imageError ? (
                <Image
                  src={product.image_url}
                  alt={product.name}
                  fill
                  className="object-cover rounded-md"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
                  <Award className="h-6 w-6 text-gray-400" />
                </div>
              )}
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h3 className="text-sm font-medium text-gray-900 truncate">
                  {product.name}
                </h3>
                <Badge variant="secondary" className={getMetalColor(product.metal_type)}>
                  {product.metal_type}
                </Badge>
              </div>
              
              <p className="text-xs text-gray-500 mb-2">
                {formatWeight(product.weight, product.weight_unit)} • {product.purity}% purity
              </p>

              <div className="flex items-center justify-between">
                <div>
                  <span className="text-lg font-bold text-gray-900">
                    {formatPrice(product.current_price)}
                  </span>
                  <div className={cn('flex items-center text-xs', getPriceChangeColor(product.price_change))}>
                    {getPriceChangeIcon(product.price_change)}
                    <span className="ml-1">
                      {product.price_change_percent > 0 ? '+' : ''}{product.price_change_percent.toFixed(2)}%
                    </span>
                  </div>
                </div>

                {showActions && (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleToggleWishlist}
                      className="p-2"
                    >
                      <Heart className={cn('h-3 w-3', isWishlisted ? 'fill-red-500 text-red-500' : '')} />
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleAddToCart}
                      disabled={!product.in_stock}
                    >
                      <ShoppingCart className="h-3 w-3 mr-1" />
                      Buy
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn(
      'group hover:shadow-lg transition-all duration-200 overflow-hidden',
      variant === 'featured' && 'ring-2 ring-amber-200',
      className
    )}>
      {/* Image Section */}
      <div className="relative aspect-square overflow-hidden">
        {product.image_url && !imageError ? (
          <Image
            src={product.image_url}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-200"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
            <Award className="h-16 w-16 text-gray-400" />
          </div>
        )}

        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {product.is_new && (
            <Badge className="bg-green-500 hover:bg-green-600">New</Badge>
          )}
          {product.is_featured && (
            <Badge className="bg-amber-500 hover:bg-amber-600">Featured</Badge>
          )}
          {product.discount_percent && (
            <Badge variant="destructive">-{product.discount_percent}%</Badge>
          )}
        </div>

        {/* Quick Actions */}
        <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Button
            size="sm"
            variant="secondary"
            onClick={handleToggleWishlist}
            className="p-2 bg-white/90 hover:bg-white"
          >
            <Heart className={cn('h-4 w-4', isWishlisted ? 'fill-red-500 text-red-500' : '')} />
          </Button>
          <Link href={`/products/${product.id}`}>
            <Button
              size="sm"
              variant="secondary"
              className="p-2 bg-white/90 hover:bg-white"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
        </div>

        {/* Stock Status */}
        {!product.in_stock && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <Badge variant="destructive" className="text-sm">Out of Stock</Badge>
          </div>
        )}
      </div>

      {/* Content Section */}
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg line-clamp-2 mb-1">
              {product.name}
            </CardTitle>
            <CardDescription className="line-clamp-2">
              {product.description}
            </CardDescription>
          </div>
          <Badge variant="secondary" className={getMetalColor(product.metal_type)}>
            {product.metal_type}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Product Details */}
        <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
          <span>{formatWeight(product.weight, product.weight_unit)}</span>
          <span>{product.purity}% purity</span>
          {product.brand && <span>{product.brand}</span>}
        </div>

        {/* Rating */}
        {product.rating && (
          <div className="flex items-center mb-3">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    'h-4 w-4',
                    i < Math.floor(product.rating!) 
                      ? 'text-yellow-400 fill-yellow-400' 
                      : 'text-gray-300'
                  )}
                />
              ))}
            </div>
            <span className="text-sm text-gray-600 ml-2">
              ({product.review_count || 0})
            </span>
          </div>
        )}

        {/* Features */}
        {product.features && product.features.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {product.features.slice(0, 3).map((feature, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <div className="text-2xl font-bold text-gray-900">
              {formatPrice(product.current_price)}
            </div>
            <div className={cn('flex items-center text-sm', getPriceChangeColor(product.price_change))}>
              {getPriceChangeIcon(product.price_change)}
              <span className="ml-1">
                {product.price_change_percent > 0 ? '+' : ''}{product.price_change_percent.toFixed(2)}%
              </span>
            </div>
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex gap-2">
            <Button
              onClick={handleAddToCart}
              disabled={!product.in_stock}
              className="flex-1"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {product.in_stock ? 'Add to Cart' : 'Out of Stock'}
            </Button>
            <Link href={`/products/${product.id}`} className="flex-1">
              <Button variant="outline" className="w-full">
                View Details
              </Button>
            </Link>
          </div>
        )}

        {/* Trust Indicators */}
        <div className="flex items-center justify-center gap-4 mt-4 pt-4 border-t text-xs text-gray-500">
          <div className="flex items-center">
            <Shield className="h-3 w-3 mr-1" />
            Certified
          </div>
          <div className="flex items-center">
            <Truck className="h-3 w-3 mr-1" />
            Free Shipping
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface ProductGridProps {
  products: Product[]
  variant?: 'default' | 'compact' | 'featured'
  columns?: 1 | 2 | 3 | 4
  showActions?: boolean
  onAddToCart?: (product: Product) => void
  onAddToWishlist?: (product: Product) => void
  className?: string
}

export function ProductGrid({
  products,
  variant = 'default',
  columns = 3,
  showActions = true,
  onAddToCart,
  onAddToWishlist,
  className
}: ProductGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }

  return (
    <div className={cn('grid gap-6', gridCols[columns], className)}>
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          variant={variant}
          showActions={showActions}
          onAddToCart={onAddToCart}
          onAddToWishlist={onAddToWishlist}
        />
      ))}
    </div>
  )
}
