'use client'

import { ReactNode } from 'react'
import { Navigation } from './Navigation'
import { CompactPriceDashboard } from '@/components/prices/PriceDashboard'
import { cn } from '@/lib/utils'

interface MainLayoutProps {
  children: ReactNode
  className?: string
  showPriceTicker?: boolean
  title?: string
  description?: string
}

export function MainLayout({ 
  children, 
  className, 
  showPriceTicker = true,
  title,
  description 
}: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />
      
      {/* Price Ticker */}
      {showPriceTicker && (
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <CompactPriceDashboard />
          </div>
        </div>
      )}

      {/* Page Header */}
      {(title || description) && (
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {title && (
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-gray-600">
                {description}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className={cn('max-w-7xl mx-auto py-6 sm:px-6 lg:px-8', className)}>
        <div className="px-4 py-6 sm:px-0">
          {children}
        </div>
      </main>
    </div>
  )
}

interface PageLayoutProps {
  children: ReactNode
  title: string
  description?: string
  actions?: ReactNode
  className?: string
  showPriceTicker?: boolean
}

export function PageLayout({ 
  children, 
  title, 
  description, 
  actions,
  className,
  showPriceTicker = true 
}: PageLayoutProps) {
  return (
    <MainLayout showPriceTicker={showPriceTicker} className={className}>
      {/* Page Header with Actions */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {title}
            </h1>
            {description && (
              <p className="text-gray-600">
                {description}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex items-center space-x-4">
              {actions}
            </div>
          )}
        </div>
      </div>

      {/* Page Content */}
      {children}
    </MainLayout>
  )
}

interface SidebarLayoutProps {
  children: ReactNode
  sidebar: ReactNode
  title?: string
  description?: string
  className?: string
  sidebarClassName?: string
  contentClassName?: string
  showPriceTicker?: boolean
}

export function SidebarLayout({
  children,
  sidebar,
  title,
  description,
  className,
  sidebarClassName,
  contentClassName,
  showPriceTicker = true
}: SidebarLayoutProps) {
  return (
    <MainLayout 
      showPriceTicker={showPriceTicker} 
      title={title}
      description={description}
      className={className}
    >
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Sidebar */}
        <aside className={cn('lg:w-80 flex-shrink-0', sidebarClassName)}>
          {sidebar}
        </aside>

        {/* Main Content */}
        <main className={cn('flex-1 min-w-0', contentClassName)}>
          {children}
        </main>
      </div>
    </MainLayout>
  )
}

interface GridLayoutProps {
  children: ReactNode
  title?: string
  description?: string
  actions?: ReactNode
  columns?: 1 | 2 | 3 | 4
  gap?: 'sm' | 'md' | 'lg'
  className?: string
  showPriceTicker?: boolean
}

export function GridLayout({
  children,
  title,
  description,
  actions,
  columns = 3,
  gap = 'md',
  className,
  showPriceTicker = true
}: GridLayoutProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  }

  const gridGap = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  }

  return (
    <PageLayout
      title={title || ''}
      description={description}
      actions={actions}
      showPriceTicker={showPriceTicker}
      className={className}
    >
      <div className={cn('grid', gridCols[columns], gridGap[gap])}>
        {children}
      </div>
    </PageLayout>
  )
}

interface ContainerProps {
  children: ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  className?: string
}

export function Container({ children, size = 'lg', className }: ContainerProps) {
  const containerSizes = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  }

  return (
    <div className={cn('mx-auto px-4 sm:px-6 lg:px-8', containerSizes[size], className)}>
      {children}
    </div>
  )
}

interface SectionProps {
  children: ReactNode
  title?: string
  description?: string
  actions?: ReactNode
  className?: string
  headerClassName?: string
  contentClassName?: string
}

export function Section({
  children,
  title,
  description,
  actions,
  className,
  headerClassName,
  contentClassName
}: SectionProps) {
  return (
    <section className={cn('space-y-6', className)}>
      {(title || description || actions) && (
        <div className={cn('flex items-center justify-between', headerClassName)}>
          <div>
            {title && (
              <h2 className="text-xl font-semibold text-gray-900 mb-1">
                {title}
              </h2>
            )}
            {description && (
              <p className="text-gray-600 text-sm">
                {description}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex items-center space-x-3">
              {actions}
            </div>
          )}
        </div>
      )}
      
      <div className={contentClassName}>
        {children}
      </div>
    </section>
  )
}
