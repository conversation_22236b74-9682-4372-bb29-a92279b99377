'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Home,
  TrendingUp,
  ShoppingCart,
  Wallet,
  BarChart3,
  Bell,
  Settings,
  User,
  ChevronRight,
  ChevronDown,
  Search,
  Filter,
  Star,
  Clock,
  DollarSign,
  PieChart,
  Activity
} from 'lucide-react'

interface SidebarItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string | number
  children?: SidebarItem[]
  description?: string
}

interface SidebarProps {
  items?: SidebarItem[]
  className?: string
  title?: string
  collapsible?: boolean
  defaultCollapsed?: boolean
}

const defaultSidebarItems: SidebarItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    description: 'Overview and quick actions'
  },
  {
    name: 'Markets',
    href: '/markets',
    icon: TrendingUp,
    children: [
      {
        name: 'Live Prices',
        href: '/markets/prices',
        icon: Activity,
        description: 'Real-time metal prices'
      },
      {
        name: 'Price Charts',
        href: '/markets/charts',
        icon: BarChart3,
        description: 'Historical price data'
      },
      {
        name: 'Market Analysis',
        href: '/markets/analysis',
        icon: PieChart,
        description: 'Market trends and insights'
      }
    ]
  },
  {
    name: 'Trading',
    href: '/trading',
    icon: ShoppingCart,
    children: [
      {
        name: 'Buy/Sell',
        href: '/trading/order',
        icon: DollarSign,
        description: 'Place new orders'
      },
      {
        name: 'Order History',
        href: '/trading/history',
        icon: Clock,
        description: 'Past transactions'
      },
      {
        name: 'Active Orders',
        href: '/trading/active',
        icon: Activity,
        badge: 2,
        description: 'Pending orders'
      }
    ]
  },
  {
    name: 'Portfolio',
    href: '/portfolio',
    icon: Wallet,
    children: [
      {
        name: 'Holdings',
        href: '/portfolio/holdings',
        icon: Wallet,
        description: 'Your bullion assets'
      },
      {
        name: 'Performance',
        href: '/portfolio/performance',
        icon: TrendingUp,
        description: 'Portfolio analytics'
      },
      {
        name: 'Watchlist',
        href: '/portfolio/watchlist',
        icon: Star,
        badge: 5,
        description: 'Tracked products'
      }
    ]
  },
  {
    name: 'Price Alerts',
    href: '/alerts',
    icon: Bell,
    badge: 3,
    description: 'Price notifications'
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'Account preferences'
  }
]

export function Sidebar({ 
  items = defaultSidebarItems, 
  className, 
  title = 'Navigation',
  collapsible = false,
  defaultCollapsed = false
}: SidebarProps) {
  const [collapsed, setCollapsed] = useState(defaultCollapsed)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const pathname = usePathname()

  const toggleCollapsed = () => {
    if (collapsible) {
      setCollapsed(!collapsed)
    }
  }

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const isExpanded = (itemName: string) => expandedItems.includes(itemName)

  return (
    <div className={cn(
      'bg-white border-r border-gray-200 transition-all duration-300',
      collapsed ? 'w-16' : 'w-64',
      className
    )}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
          )}
          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleCollapsed}
              className="p-1"
            >
              <ChevronRight className={cn(
                'h-4 w-4 transition-transform duration-200',
                collapsed ? '' : 'rotate-180'
              )} />
            </Button>
          )}
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="p-2 space-y-1">
        {items.map((item) => (
          <div key={item.name}>
            {item.children ? (
              <div>
                <button
                  onClick={() => !collapsed && toggleExpanded(item.name)}
                  className={cn(
                    'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 group',
                    isActive(item.href)
                      ? 'bg-amber-50 text-amber-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  )}
                  title={collapsed ? item.name : undefined}
                >
                  <div className="flex items-center min-w-0">
                    <item.icon className={cn(
                      'flex-shrink-0',
                      collapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3'
                    )} />
                    {!collapsed && (
                      <span className="truncate">{item.name}</span>
                    )}
                    {!collapsed && item.badge && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                  {!collapsed && (
                    <ChevronDown className={cn(
                      'h-3 w-3 transition-transform duration-200 flex-shrink-0',
                      isExpanded(item.name) ? 'rotate-180' : ''
                    )} />
                  )}
                </button>

                {/* Submenu */}
                {!collapsed && isExpanded(item.name) && item.children && (
                  <div className="ml-4 mt-1 space-y-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.name}
                        href={child.href}
                        className={cn(
                          'flex items-center px-3 py-2 text-sm rounded-lg transition-colors duration-200 group',
                          isActive(child.href)
                            ? 'bg-amber-50 text-amber-600'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        )}
                        title={child.description}
                      >
                        <child.icon className="h-4 w-4 mr-3 flex-shrink-0" />
                        <span className="truncate">{child.name}</span>
                        {child.badge && (
                          <Badge variant="secondary" className="ml-auto text-xs">
                            {child.badge}
                          </Badge>
                        )}
                      </Link>
                    ))}
                  </div>
                )}

                {/* Collapsed submenu tooltip */}
                {collapsed && item.children && (
                  <div className="absolute left-16 top-0 ml-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 border border-gray-200">
                    <div className="py-1">
                      <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100">
                        {item.name}
                      </div>
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className={cn(
                            'flex items-center px-3 py-2 text-sm transition-colors duration-200',
                            isActive(child.href)
                              ? 'bg-amber-50 text-amber-600'
                              : 'text-gray-700 hover:bg-gray-50'
                          )}
                        >
                          <child.icon className="h-4 w-4 mr-3" />
                          <div>
                            <div>{child.name}</div>
                            {child.description && (
                              <div className="text-xs text-gray-500">
                                {child.description}
                              </div>
                            )}
                          </div>
                          {child.badge && (
                            <Badge variant="secondary" className="ml-auto text-xs">
                              {child.badge}
                            </Badge>
                          )}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <Link
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 group relative',
                  isActive(item.href)
                    ? 'bg-amber-50 text-amber-600'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                )}
                title={collapsed ? item.name : item.description}
              >
                <item.icon className={cn(
                  'flex-shrink-0',
                  collapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3'
                )} />
                {!collapsed && (
                  <>
                    <span className="truncate">{item.name}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </>
                )}

                {/* Collapsed item tooltip */}
                {collapsed && (
                  <div className="absolute left-16 top-0 ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                    {item.name}
                    {item.badge && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                )}
              </Link>
            )}
          </div>
        ))}
      </nav>

      {/* Footer */}
      {!collapsed && (
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-500 text-center">
            BullionTrade v1.0
          </div>
        </div>
      )}
    </div>
  )
}

interface FilterSidebarProps {
  className?: string
  onFilterChange?: (filters: Record<string, any>) => void
}

export function FilterSidebar({ className, onFilterChange }: FilterSidebarProps) {
  const [filters, setFilters] = useState({
    metalType: '',
    priceRange: '',
    purity: '',
    weight: ''
  })

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFilterChange?.(newFilters)
  }

  return (
    <div className={cn('bg-white rounded-lg border border-gray-200 p-4', className)}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        <Filter className="h-4 w-4 text-gray-500" />
      </div>

      <div className="space-y-6">
        {/* Metal Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Metal Type
          </label>
          <select
            value={filters.metalType}
            onChange={(e) => handleFilterChange('metalType', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          >
            <option value="">All Metals</option>
            <option value="gold">Gold</option>
            <option value="silver">Silver</option>
            <option value="platinum">Platinum</option>
            <option value="palladium">Palladium</option>
          </select>
        </div>

        {/* Price Range Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Price Range
          </label>
          <select
            value={filters.priceRange}
            onChange={(e) => handleFilterChange('priceRange', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          >
            <option value="">Any Price</option>
            <option value="0-1000">₹0 - ₹1,000</option>
            <option value="1000-5000">₹1,000 - ₹5,000</option>
            <option value="5000-10000">₹5,000 - ₹10,000</option>
            <option value="10000+">₹10,000+</option>
          </select>
        </div>

        {/* Purity Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Purity
          </label>
          <select
            value={filters.purity}
            onChange={(e) => handleFilterChange('purity', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          >
            <option value="">Any Purity</option>
            <option value="999">99.9%</option>
            <option value="995">99.5%</option>
            <option value="916">91.6%</option>
            <option value="900">90.0%</option>
          </select>
        </div>

        {/* Weight Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Weight
          </label>
          <select
            value={filters.weight}
            onChange={(e) => handleFilterChange('weight', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          >
            <option value="">Any Weight</option>
            <option value="1g">1 gram</option>
            <option value="5g">5 grams</option>
            <option value="10g">10 grams</option>
            <option value="1oz">1 ounce</option>
            <option value="custom">Custom</option>
          </select>
        </div>

        {/* Clear Filters */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setFilters({ metalType: '', priceRange: '', purity: '', weight: '' })
            onFilterChange?.({ metalType: '', priceRange: '', purity: '', weight: '' })
          }}
          className="w-full"
        >
          Clear Filters
        </Button>
      </div>
    </div>
  )
}
