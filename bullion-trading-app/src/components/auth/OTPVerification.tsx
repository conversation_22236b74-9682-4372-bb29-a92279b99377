'use client'

import { useState, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2, ArrowLeft, RefreshCw } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import { otpVerificationSchema, type OTPVerificationFormData } from '@/lib/validations/auth'

interface OTPVerificationProps {
  phone: string
  type: 'registration' | 'login' | 'password_reset' | 'phone_verification'
  onBack: () => void
  onSuccess: () => void
}

export function OTPVerification({ phone, type, onBack, onSuccess }: OTPVerificationProps) {
  const [timeLeft, setTimeLeft] = useState(600) // 10 minutes
  const [canResend, setCanResend] = useState(false)
  const [resendLoading, setResendLoading] = useState(false)
  const { verifyOTP, sendOTP, loading, error } = useAuth()
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<OTPVerificationFormData>({
    resolver: zodResolver(otpVerificationSchema),
    defaultValues: {
      phone,
      otp: '',
    },
  })

  const watchedOTP = watch('otp')

  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [timeLeft])

  // Auto-focus and handle OTP input
  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus()
    }
  }, [])

  const handleOTPChange = (index: number, value: string) => {
    if (value.length > 1) {
      // Handle paste
      const pastedValue = value.slice(0, 6)
      setValue('otp', pastedValue)
      
      // Focus on the last filled input or the next empty one
      const nextIndex = Math.min(pastedValue.length - 1, 5)
      inputRefs.current[nextIndex]?.focus()
      return
    }

    // Update the OTP value
    const newOTP = watchedOTP.split('')
    newOTP[index] = value
    setValue('otp', newOTP.join(''))

    // Move to next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !watchedOTP[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  const onSubmit = async (data: OTPVerificationFormData) => {
    try {
      await verifyOTP(data.phone, data.otp, type)
      onSuccess()
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  const handleResendOTP = async () => {
    try {
      setResendLoading(true)
      await sendOTP(phone, type)
      setTimeLeft(600) // Reset timer
      setCanResend(false)
      setValue('otp', '') // Clear current OTP
      inputRefs.current[0]?.focus()
    } catch (err) {
      // Error is handled by the auth context
    } finally {
      setResendLoading(false)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getTitle = () => {
    switch (type) {
      case 'registration':
        return 'Verify Your Phone'
      case 'login':
        return 'Login Verification'
      case 'password_reset':
        return 'Reset Password'
      case 'phone_verification':
        return 'Phone Verification'
      default:
        return 'Verify OTP'
    }
  }

  const getDescription = () => {
    return `We've sent a 6-digit code to ${phone}. Enter it below to continue.`
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <div className="flex items-center space-x-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-0 h-auto"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <CardTitle className="text-2xl font-bold">{getTitle()}</CardTitle>
        </div>
        <CardDescription>{getDescription()}</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label>Enter 6-digit code</Label>
            <div className="flex space-x-2 justify-center">
              {[0, 1, 2, 3, 4, 5].map((index) => (
                <Input
                  key={index}
                  ref={(el) => (inputRefs.current[index] = el)}
                  type="text"
                  inputMode="numeric"
                  maxLength={6}
                  className="w-12 h-12 text-center text-lg font-semibold"
                  value={watchedOTP[index] || ''}
                  onChange={(e) => handleOTPChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                />
              ))}
            </div>
            {errors.otp && (
              <p className="text-sm text-red-500 text-center">{errors.otp.message}</p>
            )}
          </div>

          <div className="text-center space-y-2">
            {timeLeft > 0 ? (
              <p className="text-sm text-muted-foreground">
                Code expires in {formatTime(timeLeft)}
              </p>
            ) : (
              <p className="text-sm text-red-500">Code has expired</p>
            )}

            {canResend && (
              <Button
                type="button"
                variant="link"
                onClick={handleResendOTP}
                disabled={resendLoading}
                className="text-sm"
              >
                {resendLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Resend Code'
                )}
              </Button>
            )}
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting || loading || watchedOTP.length !== 6}
          >
            {isSubmitting || loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              'Verify Code'
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
