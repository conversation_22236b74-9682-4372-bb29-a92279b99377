'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ArrowLeft, Loader2, Mail, Phone } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/contexts/AuthContext'
import { forgotPasswordSchema, type ForgotPasswordFormData } from '@/lib/validations/auth'
import { z } from 'zod'

interface ForgotPasswordFormProps {
  onBack: () => void
  onOTPRequired: (phone: string, type: 'password_reset') => void
}

const phoneResetSchema = z.object({
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
})

type PhoneResetFormData = z.infer<typeof phoneResetSchema>

export function ForgotPasswordForm({ onBack, onOTPRequired }: ForgotPasswordFormProps) {
  const [resetMethod, setResetMethod] = useState<'email' | 'phone'>('email')
  const [isSuccess, setIsSuccess] = useState(false)
  const { resetPassword, sendOTP, loading, error } = useAuth()

  const emailForm = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  })

  const phoneForm = useForm<PhoneResetFormData>({
    resolver: zodResolver(phoneResetSchema),
  })

  const onEmailSubmit = async (data: ForgotPasswordFormData) => {
    try {
      await resetPassword(data.email)
      setIsSuccess(true)
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  const onPhoneSubmit = async (data: PhoneResetFormData) => {
    try {
      await sendOTP(data.phone, 'password_reset')
      onOTPRequired(data.phone, 'password_reset')
    } catch (err) {
      // Error is handled by the auth context
    }
  }

  if (isSuccess && resetMethod === 'email') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">Check Your Email</CardTitle>
          <CardDescription className="text-center">
            We've sent a password reset link to your email address
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 text-green-600 rounded-full mb-4">
              <Mail className="w-8 h-8" />
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              If an account with that email exists, you'll receive a password reset link shortly.
            </p>
            <p className="text-xs text-muted-foreground">
              Didn't receive the email? Check your spam folder or try again.
            </p>
          </div>
          
          <Button
            type="button"
            variant="outline"
            className="w-full"
            onClick={onBack}
          >
            Back to Sign In
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <div className="flex items-center space-x-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-0 h-auto"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <CardTitle className="text-2xl font-bold">Reset Password</CardTitle>
        </div>
        <CardDescription>
          Choose how you'd like to reset your password
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={resetMethod} onValueChange={(value) => setResetMethod(value as 'email' | 'phone')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="email" className="flex items-center space-x-2">
              <Mail className="w-4 h-4" />
              <span>Email</span>
            </TabsTrigger>
            <TabsTrigger value="phone" className="flex items-center space-x-2">
              <Phone className="w-4 h-4" />
              <span>Phone</span>
            </TabsTrigger>
          </TabsList>

          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <TabsContent value="email" className="space-y-4 mt-4">
            <form onSubmit={emailForm.handleSubmit(onEmailSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email address"
                  {...emailForm.register('email')}
                  className={emailForm.formState.errors.email ? 'border-red-500' : ''}
                />
                {emailForm.formState.errors.email && (
                  <p className="text-sm text-red-500">
                    {emailForm.formState.errors.email.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={emailForm.formState.isSubmitting || loading}
              >
                {emailForm.formState.isSubmitting || loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending reset link...
                  </>
                ) : (
                  'Send Reset Link'
                )}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="phone" className="space-y-4 mt-4">
            <form onSubmit={phoneForm.handleSubmit(onPhoneSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+91 9876543210"
                  {...phoneForm.register('phone')}
                  className={phoneForm.formState.errors.phone ? 'border-red-500' : ''}
                />
                {phoneForm.formState.errors.phone && (
                  <p className="text-sm text-red-500">
                    {phoneForm.formState.errors.phone.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  We'll send an OTP to verify your identity
                </p>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={phoneForm.formState.isSubmitting || loading}
              >
                {phoneForm.formState.isSubmitting || loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending OTP...
                  </>
                ) : (
                  'Send OTP'
                )}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
