'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  FileText, 
  Download, 
  Calendar, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Receipt,
  BarChart3,
  PieChart,
  FileSpreadsheet,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { ReportingService, ReportType, ReportFormat, ReportFilters } from '@/lib/services/reportingService'
import { useReports } from '@/hooks/useReports'

interface ReportsDashboardProps {
  className?: string
}

export function ReportsDashboard({ className }: ReportsDashboardProps) {
  const [selectedTab, setSelectedTab] = useState('overview')
  const [loading, setLoading] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // Filter states
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [financialYear, setFinancialYear] = useState(new Date().getFullYear().toString())
  const [productFilter, setProductFilter] = useState('all')
  const [tradeType, setTradeType] = useState('all')
  const [status, setStatus] = useState('all')

  const handleExportReport = async (reportType: ReportType, format: ReportFormat = 'csv') => {
    setLoading(`${reportType}-${format}`)
    setError(null)
    setSuccess(null)

    try {
      const filters: ReportFilters = {
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
        product_id: productFilter === 'all' ? undefined : productFilter,
        trade_type: tradeType === 'all' ? undefined : tradeType as any,
        status: status === 'all' ? undefined : status as any,
        financial_year: reportType === 'tax_report' ? financialYear : undefined
      }

      const { url, error: exportError } = await ReportingService.exportReport(reportType, format, filters)
      
      if (exportError) {
        setError(exportError)
        return
      }

      // Trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = `${reportType}-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up the URL
      window.URL.revokeObjectURL(url)
      
      setSuccess(`${reportType.replace('_', ' ')} exported successfully!`)
    } catch (err) {
      console.error('Export error:', err)
      setError('Failed to export report')
    } finally {
      setLoading(null)
    }
  }

  const reportCards = [
    {
      id: 'trade_summary',
      title: 'Trade Summary Report',
      description: 'Comprehensive overview of all your trading activities',
      icon: <BarChart3 className="h-6 w-6" />,
      color: 'bg-blue-500'
    },
    {
      id: 'profit_loss',
      title: 'Profit & Loss Report',
      description: 'Detailed P&L analysis with performance metrics',
      icon: <TrendingUp className="h-6 w-6" />,
      color: 'bg-green-500'
    },
    {
      id: 'tax_report',
      title: 'Tax Report',
      description: 'Capital gains and tax liability calculations',
      icon: <Receipt className="h-6 w-6" />,
      color: 'bg-orange-500'
    },
    {
      id: 'portfolio_performance',
      title: 'Portfolio Performance',
      description: 'Asset allocation and performance analytics',
      icon: <PieChart className="h-6 w-6" />,
      color: 'bg-purple-500'
    }
  ]

  const currentFinancialYear = new Date().getMonth() >= 3 
    ? new Date().getFullYear() 
    : new Date().getFullYear() - 1

  const financialYears = Array.from({ length: 5 }, (_, i) => currentFinancialYear - i)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
        <p className="text-gray-600 mt-1">
          Generate comprehensive reports for your trading activities, tax planning, and performance analysis
        </p>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {success && (
        <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <span className="text-green-700">{success}</span>
        </div>
      )}

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="reports">Generate Reports</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Available Reports</p>
                    <p className="text-2xl font-bold">4</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Calendar className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Current FY</p>
                    <p className="text-2xl font-bold">{currentFinancialYear}-{(currentFinancialYear + 1).toString().slice(-2)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Download className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Export Formats</p>
                    <p className="text-2xl font-bold">CSV, JSON</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Real-time</p>
                    <p className="text-2xl font-bold">Data</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Report Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {reportCards.map((report) => (
              <Card key={report.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 ${report.color} text-white rounded-lg`}>
                      {report.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{report.title}</CardTitle>
                      <CardDescription>{report.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleExportReport(report.id as ReportType, 'csv')}
                      disabled={loading === `${report.id}-csv`}
                    >
                      {loading === `${report.id}-csv` ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <FileSpreadsheet className="h-4 w-4 mr-2" />
                      )}
                      Export CSV
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleExportReport(report.id as ReportType, 'json')}
                      disabled={loading === `${report.id}-json`}
                    >
                      {loading === `${report.id}-json` ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <FileText className="h-4 w-4 mr-2" />
                      )}
                      Export JSON
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Report Filters</CardTitle>
              <CardDescription>
                Configure filters to customize your reports
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date-from">From Date</Label>
                  <Input
                    id="date-from"
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date-to">To Date</Label>
                  <Input
                    id="date-to"
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="financial-year">Financial Year</Label>
                  <Select value={financialYear} onValueChange={setFinancialYear}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select financial year" />
                    </SelectTrigger>
                    <SelectContent>
                      {financialYears.map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}-{(year + 1).toString().slice(-2)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="trade-type">Trade Type</Label>
                  <Select value={tradeType} onValueChange={setTradeType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select trade type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="buy">Buy Only</SelectItem>
                      <SelectItem value="sell">Sell Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={status} onValueChange={setStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Generate Reports */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {reportCards.map((report) => (
              <Card key={report.id}>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 ${report.color} text-white rounded-lg`}>
                      {report.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{report.title}</CardTitle>
                      <CardDescription>{report.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col space-y-2">
                    <div className="flex space-x-2">
                      <Button
                        className="flex-1"
                        onClick={() => handleExportReport(report.id as ReportType, 'csv')}
                        disabled={loading === `${report.id}-csv`}
                      >
                        {loading === `${report.id}-csv` ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <FileSpreadsheet className="h-4 w-4 mr-2" />
                        )}
                        Export as CSV
                      </Button>
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => handleExportReport(report.id as ReportType, 'json')}
                        disabled={loading === `${report.id}-json`}
                      >
                        {loading === `${report.id}-json` ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <FileText className="h-4 w-4 mr-2" />
                        )}
                        Export as JSON
                      </Button>
                    </div>
                    
                    {report.id === 'tax_report' && (
                      <div className="text-xs text-gray-500 mt-2">
                        <p>• Includes capital gains calculations</p>
                        <p>• FIFO method for cost basis</p>
                        <p>• Short-term vs long-term classification</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Analytics</CardTitle>
              <CardDescription>
                Coming soon: Interactive charts, performance benchmarking, and advanced portfolio analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
                <p className="text-gray-600 mb-4">
                  Interactive charts, performance benchmarking, risk analysis, and more coming soon.
                </p>
                <Badge variant="secondary">Coming Soon</Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
