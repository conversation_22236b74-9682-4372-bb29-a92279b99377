'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User as SupabaseUser } from '@supabase/supabase-js'
import { supabase, isSupabaseAvailable } from '@/lib/supabase/client'
import { User, AuthState } from '@/types'

interface AuthContextType extends AuthState {
  signUp: (email: string, password: string, full_name: string, phone?: string) => Promise<void>
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  sendOTP: (phone: string, type: 'registration' | 'login' | 'password_reset') => Promise<void>
  verifyOTP: (phone: string, otp: string, type: 'registration' | 'login' | 'password_reset') => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check if Supabase is available
    if (!isSupabaseAvailable() || !supabase) {
      console.error('Supabase client not available. Check environment variables.')
      setError('Authentication service not available. Please check configuration.')
      setLoading(false)
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session?.user) {
          await fetchUserProfile(session.user)
        }
      } catch (err) {
        console.error('Error getting initial session:', err)
        setError('Failed to load user session')
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await fetchUserProfile(session.user)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const fetchUserProfile = async (supabaseUser: SupabaseUser) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', supabaseUser.id)
        .single()

      if (error) throw error

      setUser(data)
      setError(null)
    } catch (err) {
      console.error('Error fetching user profile:', err)
      setError('Failed to load user profile')
    }
  }

  const signUp = async (email: string, password: string, full_name: string, phone?: string) => {
    try {
      console.log('SignUp called with:', { email, full_name, phone })
      setLoading(true)
      setError(null)

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: full_name,
            phone: phone || null,
          },
        },
      })

      console.log('Supabase signUp response:', { data, error })

      if (error) throw error

      // If phone is provided, send OTP for verification
      if (phone && data.user) {
        console.log('Sending OTP to phone:', phone)
        await sendOTP(phone, 'registration')
      }
    } catch (err: any) {
      console.error('SignUp error:', err)
      setError(err.message || 'Failed to create account')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      setError(null)

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error
    } catch (err: any) {
      setError(err.message || 'Failed to sign in')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    } catch (err: any) {
      setError(err.message || 'Failed to sign out')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const sendOTP = async (phone: string, type: 'registration' | 'login' | 'password_reset') => {
    try {
      setError(null)
      
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone, type }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send OTP')
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send OTP')
      throw err
    }
  }

  const verifyOTP = async (phone: string, otp: string, type: 'registration' | 'login' | 'password_reset') => {
    try {
      setError(null)
      
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone, otp, type }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to verify OTP')
      }

      // Refresh user profile if verification was successful
      if (user) {
        await refreshUser()
      }
    } catch (err: any) {
      setError(err.message || 'Failed to verify OTP')
      throw err
    }
  }

  const resetPassword = async (email: string) => {
    try {
      setError(null)
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) throw error
    } catch (err: any) {
      setError(err.message || 'Failed to send reset email')
      throw err
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    try {
      setError(null)
      
      if (!user) throw new Error('No user logged in')

      const { error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)

      if (error) throw error

      // Update local user state
      setUser({ ...user, ...updates })
    } catch (err: any) {
      setError(err.message || 'Failed to update profile')
      throw err
    }
  }

  const refreshUser = async () => {
    try {
      const { data: { user: supabaseUser } } = await supabase.auth.getUser()
      if (supabaseUser) {
        await fetchUserProfile(supabaseUser)
      }
    } catch (err) {
      console.error('Error refreshing user:', err)
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    error,
    signUp,
    signIn,
    signOut,
    sendOTP,
    verifyOTP,
    resetPassword,
    updateProfile,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
