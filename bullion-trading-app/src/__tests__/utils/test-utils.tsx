import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from '@/contexts/AuthContext'
import { PriceProvider } from '@/contexts/PriceContext'

// Mock user data for testing
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  phone: '+**********',
  full_name: 'Test User',
  is_verified: true,
  verification_level: 'basic' as const,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

// Mock portfolio data
export const mockPortfolio = {
  id: 'test-portfolio-id',
  user_id: 'test-user-id',
  total_value: 100000,
  total_invested: 95000,
  total_profit_loss: 5000,
  profit_loss_percentage: 5.26,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

// Mock holdings data
export const mockHoldings = [
  {
    id: 'holding-1',
    user_id: 'test-user-id',
    metal_type: 'gold' as const,
    quantity: 10,
    average_price: 5000,
    current_value: 52000,
    profit_loss: 2000,
    profit_loss_percentage: 4.0,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'holding-2',
    user_id: 'test-user-id',
    metal_type: 'silver' as const,
    quantity: 100,
    average_price: 480,
    current_value: 48000,
    profit_loss: 3000,
    profit_loss_percentage: 6.67,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
]

// Mock price data
export const mockPrices = {
  gold: {
    buy: 5200,
    sell: 5180,
    change: 20,
    changePercent: 0.39,
    timestamp: Date.now(),
  },
  silver: {
    buy: 480,
    sell: 478,
    change: -2,
    changePercent: -0.42,
    timestamp: Date.now(),
  },
  platinum: {
    buy: 3200,
    sell: 3180,
    change: 10,
    changePercent: 0.31,
    timestamp: Date.now(),
  },
  palladium: {
    buy: 2800,
    sell: 2780,
    change: -15,
    changePercent: -0.54,
    timestamp: Date.now(),
  },
}

// Mock orders data
export const mockOrders = [
  {
    id: 'order-1',
    user_id: 'test-user-id',
    type: 'buy' as const,
    metal_type: 'gold' as const,
    quantity: 5,
    price: 5200,
    total_amount: 26000,
    status: 'completed' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'order-2',
    user_id: 'test-user-id',
    type: 'sell' as const,
    metal_type: 'silver' as const,
    quantity: 20,
    price: 478,
    total_amount: 9560,
    status: 'pending' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
]

// Mock transactions data
export const mockTransactions = [
  {
    id: 'transaction-1',
    user_id: 'test-user-id',
    order_id: 'order-1',
    type: 'buy' as const,
    metal_type: 'gold' as const,
    quantity: 5,
    price: 5200,
    total_amount: 26000,
    fees: 260,
    net_amount: 26260,
    status: 'completed' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
]

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <PriceProvider>
          {children}
        </PriceProvider>
      </AuthProvider>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Helper functions for testing
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0))

export const mockFetch = (data: any, ok = true, status = 200) => {
  global.fetch = jest.fn().mockResolvedValue({
    ok,
    status,
    json: jest.fn().mockResolvedValue(data),
    text: jest.fn().mockResolvedValue(JSON.stringify(data)),
  })
}

export const mockFetchError = (error: string, status = 500) => {
  global.fetch = jest.fn().mockRejectedValue(new Error(error))
}

// Mock Supabase responses
export const mockSupabaseResponse = (data: any, error: any = null) => ({
  data,
  error,
  status: error ? 400 : 200,
  statusText: error ? 'Bad Request' : 'OK',
})

// Mock authentication context
export const mockAuthContext = {
  user: mockUser,
  loading: false,
  signIn: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  updateProfile: jest.fn(),
}

// Mock price context
export const mockPriceContext = {
  prices: mockPrices,
  loading: false,
  error: null,
  lastUpdated: Date.now(),
  subscribe: jest.fn(),
  unsubscribe: jest.fn(),
}

// Form testing helpers
export const fillForm = async (form: HTMLFormElement, data: Record<string, string>) => {
  const { fireEvent } = await import('@testing-library/react')
  
  Object.entries(data).forEach(([name, value]) => {
    const input = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (input) {
      fireEvent.change(input, { target: { value } })
    }
  })
}

export const submitForm = async (form: HTMLFormElement) => {
  const { fireEvent } = await import('@testing-library/react')
  fireEvent.submit(form)
}

// API testing helpers
export const mockApiResponse = (endpoint: string, response: any, method = 'GET') => {
  const mockFn = jest.fn().mockResolvedValue({
    ok: true,
    status: 200,
    json: jest.fn().mockResolvedValue(response),
  })
  
  global.fetch = jest.fn().mockImplementation((url, options) => {
    if (url.includes(endpoint) && (options?.method || 'GET') === method) {
      return mockFn()
    }
    return Promise.reject(new Error(`Unexpected API call: ${method} ${url}`))
  })
  
  return mockFn
}

export const mockApiError = (endpoint: string, error: any, status = 500, method = 'GET') => {
  global.fetch = jest.fn().mockImplementation((url, options) => {
    if (url.includes(endpoint) && (options?.method || 'GET') === method) {
      return Promise.resolve({
        ok: false,
        status,
        json: jest.fn().mockResolvedValue(error),
      })
    }
    return Promise.reject(new Error(`Unexpected API call: ${method} ${url}`))
  })
}

// Date testing helpers
export const mockDate = (date: string | Date) => {
  const mockDate = new Date(date)
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate)
  return mockDate
}

export const restoreDate = () => {
  jest.restoreAllMocks()
}

// Local storage testing helpers
export const mockLocalStorage = (data: Record<string, string> = {}) => {
  const localStorageMock = {
    getItem: jest.fn((key: string) => data[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      data[key] = value
    }),
    removeItem: jest.fn((key: string) => {
      delete data[key]
    }),
    clear: jest.fn(() => {
      Object.keys(data).forEach(key => delete data[key])
    }),
  }
  
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
  })
  
  return localStorageMock
}

// Error boundary testing
export const ErrorBoundary = ({ children }: { children: React.ReactNode }) => {
  try {
    return <>{children}</>
  } catch (error) {
    return <div data-testid="error-boundary">Something went wrong</div>
  }
}

// Custom matchers for better assertions
export const customMatchers = {
  toBeWithinRange: (received: number, floor: number, ceiling: number) => {
    const pass = received >= floor && received <= ceiling
    return {
      message: () =>
        `expected ${received} ${pass ? 'not ' : ''}to be within range ${floor} - ${ceiling}`,
      pass,
    }
  },
  
  toHaveValidEmail: (received: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const pass = emailRegex.test(received)
    return {
      message: () =>
        `expected ${received} ${pass ? 'not ' : ''}to be a valid email`,
      pass,
    }
  },
  
  toHaveValidPhone: (received: string) => {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/
    const pass = phoneRegex.test(received) && received.replace(/\D/g, '').length >= 10
    return {
      message: () =>
        `expected ${received} ${pass ? 'not ' : ''}to be a valid phone number`,
      pass,
    }
  },
}

// Extend Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeWithinRange(floor: number, ceiling: number): R
      toHaveValidEmail(): R
      toHaveValidPhone(): R
    }
  }
}

// Add custom matchers to Jest
if (typeof expect !== 'undefined') {
  expect.extend(customMatchers)
}
