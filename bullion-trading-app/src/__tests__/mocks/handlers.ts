import { rest } from 'msw'
import { mockUser, mockPortfolio, mockHoldings, mockPrices, mockOrders, mockTransactions } from '../utils/test-utils'

// API base URLs
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const METAL_RATES_API_URL = process.env.METAL_RATES_API_URL
const WHATSAPP_API_URL = process.env.WHATSAPP_API_URL

export const handlers = [
  // Authentication endpoints
  rest.post(`${SUPABASE_URL}/auth/v1/signup`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        user: mockUser,
        session: {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          expires_in: 3600,
          token_type: 'bearer',
        },
      })
    )
  }),

  rest.post(`${SUPABASE_URL}/auth/v1/token`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        user: mockUser,
        session: {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          expires_in: 3600,
          token_type: 'bearer',
        },
      })
    )
  }),

  rest.get(`${SUPABASE_URL}/auth/v1/user`, (req, res, ctx) => {
    return res(ctx.status(200), ctx.json(mockUser))
  }),

  rest.post(`${SUPABASE_URL}/auth/v1/logout`, (req, res, ctx) => {
    return res(ctx.status(204))
  }),

  // WhatsApp OTP endpoints
  rest.post(`${WHATSAPP_API_URL}/send-otp`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: 'OTP sent successfully',
        otp_id: 'mock-otp-id',
      })
    )
  }),

  rest.post(`${WHATSAPP_API_URL}/verify-otp`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: 'OTP verified successfully',
        verified: true,
      })
    )
  }),

  // Metal rates API endpoints
  rest.get(`${METAL_RATES_API_URL}/spot`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        gold: mockPrices.gold,
        silver: mockPrices.silver,
        platinum: mockPrices.platinum,
        palladium: mockPrices.palladium,
        timestamp: Date.now(),
      })
    )
  }),

  // Portfolio endpoints
  rest.get('/api/portfolio', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          portfolio: mockPortfolio,
          holdings: mockHoldings,
        },
      })
    )
  }),

  rest.get('/api/portfolio/holdings', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: mockHoldings,
      })
    )
  }),

  rest.get('/api/portfolio/performance', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          totalValue: 100000,
          totalInvested: 95000,
          totalProfitLoss: 5000,
          profitLossPercentage: 5.26,
          performanceHistory: [
            { date: '2024-01-01', value: 95000 },
            { date: '2024-01-02', value: 97000 },
            { date: '2024-01-03', value: 100000 },
          ],
        },
      })
    )
  }),

  // Orders endpoints
  rest.get('/api/orders', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: mockOrders,
      })
    )
  }),

  rest.post('/api/orders', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          id: 'new-order-id',
          ...mockOrders[0],
          status: 'pending',
        },
      })
    )
  }),

  rest.patch('/api/orders/:orderId', (req, res, ctx) => {
    const { orderId } = req.params
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          ...mockOrders[0],
          id: orderId,
          status: 'cancelled',
        },
      })
    )
  }),

  // Transactions endpoints
  rest.get('/api/transactions', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: mockTransactions,
      })
    )
  }),

  rest.get('/api/transactions/reports', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          summary: {
            totalTransactions: 10,
            totalVolume: 500000,
            totalFees: 5000,
            profitLoss: 25000,
          },
          transactions: mockTransactions,
        },
      })
    )
  }),

  // Prices endpoints
  rest.get('/api/prices', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: mockPrices,
      })
    )
  }),

  rest.get('/api/prices/history', (req, res, ctx) => {
    const metal = req.url.searchParams.get('metal') || 'gold'
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: [
          { timestamp: Date.now() - 86400000, price: 5180 },
          { timestamp: Date.now() - 43200000, price: 5190 },
          { timestamp: Date.now(), price: 5200 },
        ],
      })
    )
  }),

  // User profile endpoints
  rest.get('/api/user/profile', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: mockUser,
      })
    )
  }),

  rest.patch('/api/user/profile', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          ...mockUser,
          full_name: 'Updated Test User',
        },
      })
    )
  }),

  // Security endpoints
  rest.get('/api/security/test', (req, res, ctx) => {
    const action = req.url.searchParams.get('action')
    
    switch (action) {
      case 'dashboard':
        return res(
          ctx.status(200),
          ctx.json({
            success: true,
            data: {
              currentMetrics: {
                failedLogins: 0,
                suspiciousActivities: 0,
                rateLimitExceeded: 0,
                maliciousRequests: 0,
                unauthorizedAccess: 0,
                systemErrors: 0,
                activeUsers: 5,
                totalRequests: 1000,
                averageResponseTime: 150,
              },
              activeAlerts: [],
              riskLevel: 'LOW',
            },
          })
        )
      
      case 'test':
        return res(
          ctx.status(200),
          ctx.json({
            success: true,
            data: {
              summary: {
                total: 20,
                passed: 18,
                failed: 2,
                critical: 0,
                high: 1,
                medium: 1,
                low: 18,
              },
              criticalIssues: [],
              recommendations: ['✅ All security tests passed - system is secure'],
            },
          })
        )
      
      default:
        return res(ctx.status(400), ctx.json({ error: 'Invalid action' }))
    }
  }),

  // Error handlers for testing error scenarios
  rest.get('/api/error/500', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }))
  }),

  rest.get('/api/error/401', (req, res, ctx) => {
    return res(ctx.status(401), ctx.json({ error: 'Unauthorized' }))
  }),

  rest.get('/api/error/403', (req, res, ctx) => {
    return res(ctx.status(403), ctx.json({ error: 'Forbidden' }))
  }),

  rest.get('/api/error/404', (req, res, ctx) => {
    return res(ctx.status(404), ctx.json({ error: 'Not found' }))
  }),

  // Rate limiting test endpoint
  rest.get('/api/rate-limit-test', (req, res, ctx) => {
    return res(ctx.status(429), ctx.json({ error: 'Too many requests' }))
  }),

  // Network error simulation
  rest.get('/api/network-error', (req, res, ctx) => {
    return res.networkError('Network connection failed')
  }),
]

// Error handlers for unhandled requests
export const errorHandlers = [
  rest.get('*', (req, res, ctx) => {
    console.warn(`Unhandled GET request to ${req.url}`)
    return res(ctx.status(404), ctx.json({ error: 'Not found' }))
  }),
  
  rest.post('*', (req, res, ctx) => {
    console.warn(`Unhandled POST request to ${req.url}`)
    return res(ctx.status(404), ctx.json({ error: 'Not found' }))
  }),
]
