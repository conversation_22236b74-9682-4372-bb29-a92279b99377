-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bullion_products_updated_at BEFORE UPDATE ON public.bullion_products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON public.trades
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', ''));
    RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update portfolio after trade completion
CREATE OR REPLACE FUNCTION update_portfolio_after_trade()
RETURNS TRIGGER AS $$
DECLARE
    current_portfolio RECORD;
    new_quantity DECIMAL(15,6);
    new_avg_price DECIMAL(15,2);
    new_total_invested DECIMAL(15,2);
BEGIN
    -- Only process completed trades
    IF NEW.status != 'completed' THEN
        RETURN NEW;
    END IF;

    -- Get current portfolio
    SELECT * INTO current_portfolio
    FROM public.portfolios
    WHERE user_id = NEW.user_id AND product_id = NEW.product_id;

    IF NEW.type = 'buy' THEN
        IF current_portfolio IS NULL THEN
            -- Create new portfolio entry
            INSERT INTO public.portfolios (
                user_id, product_id, quantity, average_buy_price, 
                total_invested, current_value, profit_loss, profit_loss_percentage
            ) VALUES (
                NEW.user_id, NEW.product_id, NEW.quantity, NEW.price_per_unit,
                NEW.total_amount, NEW.total_amount, 0, 0
            );
        ELSE
            -- Update existing portfolio
            new_quantity := current_portfolio.quantity + NEW.quantity;
            new_total_invested := current_portfolio.total_invested + NEW.total_amount;
            new_avg_price := new_total_invested / new_quantity;

            UPDATE public.portfolios SET
                quantity = new_quantity,
                average_buy_price = new_avg_price,
                total_invested = new_total_invested,
                last_updated = NOW()
            WHERE user_id = NEW.user_id AND product_id = NEW.product_id;
        END IF;
    ELSIF NEW.type = 'sell' AND current_portfolio IS NOT NULL THEN
        -- Handle sell order
        new_quantity := current_portfolio.quantity - NEW.quantity;
        
        IF new_quantity <= 0 THEN
            -- Remove from portfolio if quantity becomes zero or negative
            DELETE FROM public.portfolios
            WHERE user_id = NEW.user_id AND product_id = NEW.product_id;
        ELSE
            -- Update portfolio with reduced quantity
            new_total_invested := current_portfolio.total_invested * (new_quantity / current_portfolio.quantity);
            
            UPDATE public.portfolios SET
                quantity = new_quantity,
                total_invested = new_total_invested,
                last_updated = NOW()
            WHERE user_id = NEW.user_id AND product_id = NEW.product_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for portfolio updates
CREATE TRIGGER update_portfolio_on_trade_completion
    AFTER UPDATE ON public.trades
    FOR EACH ROW EXECUTE FUNCTION update_portfolio_after_trade();

-- Function to update current values in portfolio
CREATE OR REPLACE FUNCTION update_portfolio_current_values()
RETURNS TRIGGER AS $$
BEGIN
    -- Update all portfolios for this product
    UPDATE public.portfolios p SET
        current_value = p.quantity * NEW.current_price,
        profit_loss = (p.quantity * NEW.current_price) - p.total_invested,
        profit_loss_percentage = CASE 
            WHEN p.total_invested > 0 THEN 
                (((p.quantity * NEW.current_price) - p.total_invested) / p.total_invested) * 100
            ELSE 0
        END,
        last_updated = NOW()
    WHERE p.product_id = NEW.id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for portfolio value updates when product price changes
CREATE TRIGGER update_portfolio_values_on_price_change
    AFTER UPDATE OF current_price ON public.bullion_products
    FOR EACH ROW EXECUTE FUNCTION update_portfolio_current_values();

-- Function to check and trigger price alerts
CREATE OR REPLACE FUNCTION check_price_alerts()
RETURNS TRIGGER AS $$
BEGIN
    -- Check for alerts that should be triggered
    UPDATE public.price_alerts SET
        triggered_at = NOW(),
        is_active = FALSE
    WHERE product_id = NEW.id
        AND is_active = TRUE
        AND (
            (condition = 'above' AND NEW.current_price >= target_price) OR
            (condition = 'below' AND NEW.current_price <= target_price)
        );

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for price alerts
CREATE TRIGGER check_alerts_on_price_change
    AFTER UPDATE OF current_price ON public.bullion_products
    FOR EACH ROW EXECUTE FUNCTION check_price_alerts();

-- Function to clean up expired OTPs
CREATE OR REPLACE FUNCTION cleanup_expired_otps()
RETURNS void AS $$
BEGIN
    DELETE FROM public.otp_verifications
    WHERE expires_at < NOW() AND is_verified = FALSE;
END;
$$ language 'plpgsql';

-- Function to audit important changes
CREATE OR REPLACE FUNCTION audit_changes()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.audit_logs (
        user_id, action, table_name, record_id, old_values, new_values
    ) VALUES (
        COALESCE(NEW.user_id, OLD.user_id),
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
        CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW) ELSE NULL END
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Create audit triggers for important tables
CREATE TRIGGER audit_trades
    AFTER INSERT OR UPDATE OR DELETE ON public.trades
    FOR EACH ROW EXECUTE FUNCTION audit_changes();

CREATE TRIGGER audit_portfolios
    AFTER INSERT OR UPDATE OR DELETE ON public.portfolios
    FOR EACH ROW EXECUTE FUNCTION audit_changes();

-- Function to get user portfolio summary
CREATE OR REPLACE FUNCTION get_user_portfolio_summary(user_uuid UUID)
RETURNS TABLE (
    total_value DECIMAL(15,2),
    total_invested DECIMAL(15,2),
    total_profit_loss DECIMAL(15,2),
    total_profit_loss_percentage DECIMAL(8,4),
    product_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(p.current_value), 0) as total_value,
        COALESCE(SUM(p.total_invested), 0) as total_invested,
        COALESCE(SUM(p.profit_loss), 0) as total_profit_loss,
        CASE 
            WHEN COALESCE(SUM(p.total_invested), 0) > 0 THEN
                (COALESCE(SUM(p.profit_loss), 0) / COALESCE(SUM(p.total_invested), 1)) * 100
            ELSE 0
        END as total_profit_loss_percentage,
        COUNT(*)::INTEGER as product_count
    FROM public.portfolios p
    WHERE p.user_id = user_uuid AND p.quantity > 0;
END;
$$ language 'plpgsql' SECURITY DEFINER;
