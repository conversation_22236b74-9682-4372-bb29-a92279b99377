-- Insert sample bullion products
INSERT INTO public.bullion_products (name, symbol, type, purity, weight, weight_unit, current_price, price_per_unit, description) VALUES
-- Gold products
('Gold 24K', 'GOLD24K', 'gold', '24K (99.9%)', 1, 'oz', 65000.00, 'per ounce', 'Pure 24 karat gold bullion'),
('Gold 22K', 'GOLD22K', 'gold', '22K (91.6%)', 1, 'oz', 59500.00, 'per ounce', '22 karat gold bullion'),
('Gold 10g Bar', 'GOLD10G', 'gold', '24K (99.9%)', 10, 'gram', 6500.00, 'per 10 grams', '10 gram pure gold bar'),
('Gold 1g Coin', 'GOLD1G', 'gold', '24K (99.9%)', 1, 'gram', 650.00, 'per gram', '1 gram gold coin'),

-- Silver products
('Silver 999', 'SILVER999', 'silver', '99.9%', 1, 'oz', 850.00, 'per ounce', 'Pure silver bullion'),
('Silver 100g Bar', 'SILVER100G', 'silver', '99.9%', 100, 'gram', 8500.00, 'per 100 grams', '100 gram silver bar'),
('Silver 10g Coin', 'SILVER10G', 'silver', '99.9%', 10, 'gram', 850.00, 'per 10 grams', '10 gram silver coin'),
('Silver 1kg Bar', 'SILVER1KG', 'silver', '99.9%', 1, 'kg', 85000.00, 'per kg', '1 kilogram silver bar'),

-- Platinum products
('Platinum 999', 'PLATINUM999', 'platinum', '99.9%', 1, 'oz', 32000.00, 'per ounce', 'Pure platinum bullion'),
('Platinum 10g Bar', 'PLATINUM10G', 'platinum', '99.9%', 10, 'gram', 3200.00, 'per 10 grams', '10 gram platinum bar'),

-- Palladium products
('Palladium 999', 'PALLADIUM999', 'palladium', '99.9%', 1, 'oz', 28000.00, 'per ounce', 'Pure palladium bullion'),
('Palladium 10g Bar', 'PALLADIUM10G', 'palladium', '99.9%', 10, 'gram', 2800.00, 'per 10 grams', '10 gram palladium bar');

-- Insert initial price history data for the last 30 days
DO $$
DECLARE
    product_record RECORD;
    day_offset INTEGER;
    base_price DECIMAL(15,2);
    price_variation DECIMAL(15,2);
    daily_price DECIMAL(15,2);
BEGIN
    FOR product_record IN SELECT id, current_price FROM public.bullion_products LOOP
        base_price := product_record.current_price;
        
        FOR day_offset IN 0..29 LOOP
            -- Generate some realistic price variation (±5%)
            price_variation := (RANDOM() - 0.5) * 0.1 * base_price;
            daily_price := base_price + price_variation;
            
            INSERT INTO public.price_history (product_id, price, timestamp)
            VALUES (
                product_record.id,
                daily_price,
                NOW() - INTERVAL '1 day' * day_offset
            );
        END LOOP;
    END LOOP;
END $$;

-- Create a scheduled function to clean up expired OTPs (runs every hour)
SELECT cron.schedule(
    'cleanup-expired-otps',
    '0 * * * *', -- Every hour
    'SELECT cleanup_expired_otps();'
);

-- Insert some sample price alerts for demonstration (these would normally be created by users)
-- Note: These will be created when users actually create alerts through the app
